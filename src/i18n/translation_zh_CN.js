export default {
  "j-agent-web_pages_category_components_CategorySelect_NoCategoryDataIsAvailable": "没有可用的类目数据",
  "j-agent-web_pages_category_LevelCategory": "一级类目",
  "j-agent-web_pages_category_ApplyFilterCriteria": "应用筛选条件:",
  "j-agent-web_pages_category_components_CategorySelect_LevelCategory": "一级类目",
  "j-agent-web_pages_category_components_CategorySelect_SecondaryCategory": "二级类目",
  "j-agent-web_pages_category_components_CategorySelect_LevelCategory_1": "三级类目",
  "j-agent-web_pages_category_ClosedSuccessfully": "关闭成功",
  "j-agent-web_pages_category_FailedToClose": "关闭失败",
  "j-agent-web_pages_comments_CurrentlyScoringIsNotSupported": "暂不支持评分",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CommentsDatacommdesc": "评论（{{dataCommDesc}}）",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentFeatures": "客户评论特征：",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList": "热销榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList": "飙升榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList": "潜力榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_SalesList": "销量榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_DailySalesList": "日销量榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_WeeklySalesList": "周销量榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_MonthlySalesList": "月销量榜",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_Comments": "评论",
  "j-agent-web_utils_util_AYearAgo": "一年前",
  "j-agent-web_utils_util_YearsYearsAgo": "{{years}}年前",
  "j-agent-web_utils_util_AMonthAgo": "一个月前",
  "j-agent-web_utils_util_MonthsMonthsAgo": "{{months}}个月前",
  "j-agent-web_utils_util_ADayAgo": "一天前",
  "j-agent-web_utils_util_DaysDaysAgo": "{{days}}天前",
  "j-agent-web_utils_util_AnHourAgo": "一个小时前",
  "j-agent-web_utils_util_HoursHoursAgo": "{{hours}}小时前",
  "j-agent-web_utils_util_OneMinuteAgo": "一分钟前",
  "j-agent-web_utils_util_MinutesMinutesAgo": "{{minutes}}分钟前",
  "j-agent-web_utils_util_JustNow": "刚刚",
  "j-agent-web_utils_util_SecondsSecondsAgo": "{{seconds}}秒前",
  "j-agent-web_pages_commodityDetail_components_ProductPage_SorryThereIsNoProduct": "抱歉，暂无商品数据",
  "j-agent-web_pages_category_SubscriptionSuccessful": "订阅成功",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadFailed": "{{fileName}} 上传失败",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadedSuccessfully": "{{fileName}} 上传成功",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFailed": "上传失败:",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFiles": "上传文件",
  "j-agent-web_pages_agentForm_components_FileUploader_SupportedFormatPhtASingle": "支持格式：.pht，单个文件不超过200MB，最多上传",
  "j-agent-web_pages_agentForm_components_FileUploader_Files": "个文件",
  "j-agent-web_pages_agentForm_components_FileUploader_PreviewImage": "预览图片",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelId": "模型id",
  "j-agent-web_pages_agentForm_components_FormComponent_Copy": "复制",
  "j-agent-web_pages_agentForm_components_FormComponent_CopiedSuccessfully": "复制成功",
  "j-agent-web_pages_agentForm_components_FormComponent_UpdatedSuccessfully": "更新成功",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUploadFailedErr": "模型上传失败{{err}}",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionError": "表单提交错误:",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionFailedPleaseTry": "表单提交失败，请重试！",
  "j-agent-web_pages_agentForm_components_FormComponent_TheFormHasBeenReset": "表单已重置",
  "j-agent-web_pages_agentForm_components_FormComponent_SelectAModelToUpdate": "请选择要更新的模型",
  "j-agent-web_pages_agentForm_components_FormComponent_TheModelToBeUpdated": "选择了需要更新的模型",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelName": "模型名称",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterAModelName": "请输入模型名称",
  "j-agent-web_pages_agentForm_components_FormComponent_UploadModelFiles": "模型文件上传",
  "j-agent-web_pages_agentForm_components_FormComponent_PromptEditingSupportsMarkdown": "Prompt编辑支持Markdown",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterPrompt": "请输入prompt",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpdate": "模型更新",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpload": "模型上传",
  "j-agent-web_pages_agentForm_components_FormComponent_Reset": "重置",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Bold": "粗体",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Italic": "斜体",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_OrderedList": "有序列表",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_UnorderedList": "无序列表",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Link": "链接",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Edit": "编辑",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_MarkdownFormatEditingIsSupported": "支持 Markdown 格式编辑，可以输入文本、列表、链接等...",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Preview": "预览",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_NoContentPreview": "无内容预览",
  "j-agent-web_pages_agentForm_AddModel": "新增模型",
  "j-agent-web_pages_agentForm_UpdateModelPrompt": "更新模型prompt",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainOssConfiguration": "获取OSS配置失败:",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainUploadCredentials": "获取上传凭证失败",
  "j-agent-web_pages_agentForm_services_ossService_FailedToUploadTheFile": "上传文件失败:",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMost": "该平台昨日全品类（或订阅某些品类）当日销量最多的商品；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostDaily": "该平台昨日全品类（或订阅某些品类）当日销量最多的商品；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostWeekly": "该平台本周全品类（或订阅某些品类）当周销量最多的商品；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostMonthly": "该平台本月全品类（或订阅某些品类）当月销量最多的商品；",
  "j-agent-web_pages_cardTips_NewProductsWithTheLargest": "此平台所有类别（或订阅类别）在过去30天内推出后喜欢人数最多的新产品；",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldThe": "该平台昨日全品类（或订阅某些品类）当日销量增速相较前日最多的商品；",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldAll": "此平台所有类别（或订阅类别）在过去30天上榜天数占比最长的商品。",
  "j-agent-web_pages_cardTips_DataDescription": "数据说明",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_StoreNameProductstorename": "店铺：",
  "j-agent-web_pages_productCuration_components_ProductList_Rating": "评分：",
  "j-agent-web_pages_productCuration_ProductSelectionCenter": "商品选品中心",
  "j-agent-web_pages_productCuration_UnableToObtainBizid": "获取不到bizId",
  "j-agent-web_pages_productCuration_FailedToObtainProductList": "获取商品列表失败",
  "j-agent-web_pages_productCuration_FailedToSubmitTheProduct": "提交商品失败",
  "j-agent-web_pages_productCuration_LoadingGoods": "加载商品中...",
  "j-agent-web_pages_productCuration_ProductslengthItemsInTotalSelectedcount": "共 {{productsLength}} 个商品，已选择 {{selectedCount}} 个商品",
  "j-agent-web_pages_productCuration_DeselectAll": "取消全选",
  "j-agent-web_pages_productCuration_SelectAllCurrentPage": "全选当前页",
  "j-agent-web_pages_productCuration_NoProductDataAvailable": "暂无商品数据",
  "j-agent-web_pages_productCuration_SubmitSelectionSelectedcount": "提交选品({{selectedCount}})",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing": "1688相似商品",
  "j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData": "获取数据失败",
  "j-agent-web_pages_productOrigin_components_ProductPage_ZozoGrid": "ZOZOTOWN价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_AmazonGrid": "Amazon价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_RednoteGrid": "参考价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_TiktokGrid": "抖音价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_TaobaoGrid": "淘宝价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_AlibabaGrid": "1688Japan价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_RakutenGrid": "乐天价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_ManualGrid": "价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_Details": "详情",
  "j-agent-web_pages_productOrigin_components_ProductPage_Favorites": "收藏数",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductdatafavnumPeople": "{{productDataFavNum}} 人",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoCollection": "暂无收藏",
  "j-agent-web_pages_productOrigin_components_ProductPage_List": "榜单",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductRating": "商品评分",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoScore": "暂无评分",
  "j-agent-web_pages_productOrigin_components_ProductPage_SimilarProducts": "1688相似商品",
  "j-agent-web_pages_category_components_CategorySelect_AllCategories": "全部类目",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_FindSimilarity": "找相似",
  "j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume": "今日销量",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfLikes": "喜欢数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_DailyGrowth": "日增长",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfDaysOnThe": "上榜天数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_SalesVolume": "销量",
  "j-agent-web_pages_productOriginV2_components_ProductPage_MonthlySales": "近90天销量",
  "j-agent-web_pages_productOriginV2_components_ProductPage_RepurchaseRate": "回购率",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ProductEvaluation": "商品评价",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ListSource": "榜单来源",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NoListInformation": "暂无榜单信息",
  "j-agent-web_pages_productOriginV2_components_ProductPage_FactoryPrice": "工厂直销价格",
  "j-agent-web_pages_productCuration_components_ProductList_ViewProductDetails": "查看商品详情",
  "j-agent-web_pages_productOrigin_components_ProductPage_ShareContent": "寻找1688japan的你，来这里！海量中国商品直供日本，价格更低、选择更多！",
  "j-agent-web_pages_productCuration_SuccessfullySubmitted": "已成功提交 {{successItemListLen}} 个商品到Feeds流，失败 {{failItemListLen}} 个商品",
  "j-dingtalk-web_components_ImageSwiper_ProductPortrait": "商品图片",
  "j-dingtalk-web_components_OptimizedImage_PortraitFailure": "图像加载失败",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_ForProductInformationSee": "未找到商品",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pieces": "件",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ProductPortrait": "商品图片",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NoMore": "没有更多了",
  "j-dingtalk-web_pages_category_FailedToObtainCategoryData": "获取类目数据失败",
  "j-dingtalk-web_pages_category_SelectASecondaryCategory": "请选择二级类目",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_MoreProducts": "更多商品",
  "j-dingtalk-web_pages_category_CategorySelectionSucceeded": "订阅成功",
  "j-dingtalk-web_pages_category_CategorySelectionFailed": "订阅失败，请重试",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_MoreDetails": "查看详情",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CommentDetails": "评论详情",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentCount": "{{commentCount}}条全球评分",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_StarRating": "{{rating}}颗星",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_ScoreFullScore": "{{score}}分（满分5分）",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CustomerFeedback": "客户反馈",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_GeneratedByAiBasedOn": "基于客户提交内容，由AI生成",
  "j-agent-web_pages_productOrigin_components_ProductPage_Price": "价格",
  "j-dingtalk-web_pages_premiumStore_StoreDetails": "店铺详情",
  "j-dingtalk-web_pages_premiumStore_ComprehensiveEvaluation": "综合评价：",
  "j-dingtalk-web_pages_premiumStore_ProductQuality": "商品质量：",
  "j-dingtalk-web_pages_premiumStore_DeliverySpeed": "发货速度：",
  "j-dingtalk-web_pages_premiumStore_ConsultingServices": "咨询服务：",
  "j-dingtalk-web_pages_premiumStore_SimilarProducts": "类似商品",
  "j-dingtalk-web_pages_topMerchant_GoodNumberDetails": "博主详情",
  "j-dingtalk-web_pages_topMerchant_FailedToObtainData": "获取数据失败",
  "j-dingtalk-web_pages_topMerchant_Fans": "粉丝",
  "j-dingtalk-web_pages_topMerchant_NumberOfInteractions": "互动次数",
  "j-dingtalk-web_pages_topMerchant_DataOverview": "数据概览",
  "j-dingtalk-web_pages_topMerchant_NumberOfLikesReceived": "获赞数",
  "j-dingtalk-web_pages_topMerchant_NumberOfNotes": "笔记数",
  "j-dingtalk-web_pages_topMerchant_NumberOfCooperativeBrands": "合作品牌数",
  "j-dingtalk-web_pages_topMerchant_Mockmerchantinfobrandcount": "{{merchantInfoGoodsNum}}个",
  "j-dingtalk-web_pages_topMerchant_NumberOfConcerns": "关注数",
  "j-dingtalk-web_pages_topMerchant_StyleOverview": "风格概览",
  "j-dingtalk-web_pages_topMerchant_HighestInteraction": "互动最高",
  "j-dingtalk-web_pages_topMerchant_RecentlyReleased": "最近发布",
  "j-dingtalk-web_pages_topMerchant_TheGoodsMentionedInThis": "该笔记中提到的商品",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts": "类似商品",
  "j-dingtalk-web_pages_topMerchant_ProductsSoldByTa": "TA售卖的商品",
  "j-dingtalk-web_pages_topMerchant_HighestSalesVolume": "销量最高",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts_1": "类似商品",
  "j-dingtalk-web_components_ImageSwiper_LookingForSimilarProducts": "寻找类似产品",
  "j-agent-web_pages_productOrigin_components_ProductPage_CopiedToClipboard": "productId 已复制",
  "j-dingtalk-web_pages_category_CategorySelect_DailyNewspaper": "日报",
  "j-dingtalk-web_pages_category_CategorySelect_WeeklyNewspaper": "周报",
  "j-dingtalk-web_pages_category_CategorySelect_MonthlyReport": "月报",
  "j-dingtalk-web_pages_category_CategorySelect_EveryWorkingDay": "每个工作日",
  "j-dingtalk-web_pages_category_CategorySelect_EveryDay": "每天",
  "j-dingtalk-web_pages_category_CategorySelect_EveryFriday": "每周五",
  "j-dingtalk-web_pages_category_CategorySelect_ThOfEachMonth": "每月1日",
  "j-dingtalk-web_pages_category_CategorySelect_SubscriptionSettings": "订阅设置",
  "j-dingtalk-web_pages_category_CategorySelect_SelectDate": "选择日期",
  "j-dingtalk-web_pages_category_CategorySelect_SelectTime": "选择时间",
  "j-dingtalk-web_pages_category_CategorySelect_Cancel": "取消",
  "j-dingtalk-web_pages_category_CategorySelect_Confirm": "确认",
  "j-dingtalk-web_pages_category_Comprehensive": "综合",
  "j-dingtalk-web_pages_category_PleaseSelectALevelCategory": "请选择一级类目",
  "j-dingtalk-web_pages_category_SelectALevelCategory": "请选择三级类目",
  "j-dingtalk-web_components_Loading_Loading": "加载中",
  "j-dingtalk-web_pages_category_CategorySelect_Loading": "加载中",
  "j-dingtalk-web_pages_category_CategorySwitchingFailedPleaseTry": "类目切换失败，请重试",
  "j-dingtalk-web_pages_premiumStore_MonthlySalesProductmonthsolddisplayPieces": "月销售量{{productMonthSoldDisplay}}件",
  "j-dingtalk-web_pages_premiumStore_FailedToObtainStoreData": "店铺数据加载失败~",
  "j-dingtalk-web_components_SwipeableProductCard_NotInterested": "不感兴趣",
  "j-dingtalk-web_components_SwipeableProductCard_Interested": "感兴趣",
  "j-dingtalk-web_components_SwipeableProductCard_Approve": "通过",
  "j-dingtalk-web_components_SwipeableProductCard_Reject": "不通过",
  "j-dingtalk-web_components_SwipeableProductList_NoProductDataAvailable": "暂无商品数据",
  "j-dingtalk-web_components_SwipeableProductList_SlideLeftAndRightTo": "← 左右滑动切换商品 →",
  "j-dingtalk-web_components_SwipeableProductList_IMNotInterestedIn": "↑ 上滑不感兴趣 ↓ 下滑感兴趣",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFakeAndInferiorProducts": "举报假冒伪劣",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportThatThisProductIs": "举报此商品是假冒伪劣商品",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotYet": "暂不",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ConfirmReport": "确认举报",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_TheParameterIsIncorrectAnd": "参数错误，无法举报",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportSuccessful": "举报成功",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFailedPleaseTryAgain": "举报失败，请稍后重试",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_UnknownError": "未知错误",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditSuccess": "审核{{status}}",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditFailed": "审核失败，请稍后重试",
  "j-dingtalk-web_pages_sourcing_components_Page_MarkedAsInterested": "已标记为感兴趣",
  "j-dingtalk-web_pages_sourcing_components_Page_MarkedAsNotInterested": "已标记为不感兴趣",
  "j-dingtalk-web_pages_sourcing_components_Page_AuditSuccess": "审核{{status}}成功",
  "j-dingtalk-web_pages_sourcing_components_Page_AuditFailed": "审核失败，请稍后重试",
  "j-dingtalk-web_pages_sourcing_components_Page_SimilarSources": "1688相似货源",
  "j-dingtalk-web_pages_topMerchant_Favorites": "收藏数",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pass": "通过",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotPassed": "不通过",
  "j-dingtalk-web_utils_util_NumberUnitTenThousand": "万",
  "j-dingtalk-web_utils_util_NumberUnitHundredMillion": "亿",
  "j-dingtalk-web_pages_topMerchant_LoadMore": "加载更多",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_UseThisFeatureInThe": "请在7钉客户端中使用此功能",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_InitializingPleaseTryAgainLater": "正在初始化，请稍后重试",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully": "图片上传成功",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed": "图片上传失败",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailedPleaseTry": "图片上传失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage": "点击上传图片",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading": "上传中...",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageSizeError": "图片尺寸不符合要求，要求：最小{{minWidth}}x{{minHeight}}，最大{{maxWidth}}x{{maxHeight}}。",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageLoadError": "图片加载失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ImageSpecification": "图片规范",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_MaterialType": "素材类型：",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheElementsAreSimpleThe": "1、元素简洁，光线充足、商品清晰、轮廓明显呈现",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ItIsRecommendedToUpload": "2、建议上传有模特图、或真人展示商品图（半身、近景效果更佳）",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Size": "尺寸规格：",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_SupportsJpgPngFormatWith": "1、支持 JPG/PNG格式，大小不超过 5M",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheShortSideIsNot": "2、短边不小于 300PX，长宽比在5:2 和 2:5之间",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CorrectExample": "正确示例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_HandheldProductWithClearText": "手持商品，文字清晰",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheProductIsClearAnd": "商品清晰，轮廓明显",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelHalfLengthDisplayClothing": "模特半身展示服装",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelTrialProducts": "模特试用产品",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ErrorExample": "错误示例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_StitchingPicture": "拼接图片",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_UnclearSubject": "主体不清",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CreativeDescriptionReference": "创意描述参考",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_DescribeThePictureAndAction": "结合图片描述你想生成的画面和动作；建议使用“主体+动作”的描述方式，如“模特微笑向前走”",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ReferenceExample": "参考示例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_MenSlowlyPickUpThe": "男人缓缓拿起酒杯",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptMenSlowlyPickUp": "Prompt：男人缓缓拿起酒杯",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheLensSlowlyRotatesTo": "镜头缓缓向右侧旋转，展示游戏主机机箱",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptTheLensSlowlyRotates": "Prompt：镜头缓缓向右侧旋转，展示游戏主机机箱",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelPosingShowingYogaClothes": "模特摆造型，展示瑜伽服",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptModelPosingShowingYoga": "Prompt：模特摆造型，展示瑜伽服",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ContentYouDonTWant": "你不希望出现的内容（非必填）",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CopiedToCreativeDescription": "已复制到创意描述",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription": "复制到创意描述",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseUploadAnImage": "请上传图片",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseEnterAForwardDescription": "请输入正向描述",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Hd": "高清",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_StandardClear": "标清",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_FirstFrameVideoPicture": "首帧视频图片",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_YourCreativeDescription": "你的创意描述",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsModelDisplay": "例如：模特展示产品",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsDistortionDistortion": "输入比如失真、扭曲、变形、低质量等",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDefinition": "视频清晰度",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDuration": "视频时长",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Generating": "生成中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_GenerateNow": "立即生成",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_HelpInformation": "帮助信息",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Generating": "生成中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ItIsExpectedToBe": "预计在1～3分钟完成，再此期间您可以退出",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Hd": "高清",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_StandardClear": "标清",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_PleaseClickTheFullScreen": "请点击视频右下角全屏按钮",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ThePraiseIsCanceled": "取消点赞成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ThumbsUpSuccessfully": "点赞成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry": "操作失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_TheCancellationPointIsStepped": "取消点踩成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ClickSuccessfully": "点踩成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifAlreadyExistsStartDownloading": "GIF已存在，开始下载...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationCompleted": "GIF生成完成！",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedToStart": "GIF生成启动失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationHasStartedPlease": "GIF生成已开始，请稍候...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedPleaseTry": "GIF生成失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailed": "GIF生成失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationTimeout": "GIF生成超时",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_UnknownStatusResultstatus": "未知状态: {{resultStatus}}",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegeneratingVideo": "正在重新生成视频...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationHasStartedPleaseWait": "重新生成已开始，请稍候...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailed": "重新生成失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailedPleaseTryAgain": "重新生成失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ConfirmDeletion": "确认删除",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_AreYouSureYouWant": "您确定要删除此视频吗？",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Cancel": "取消",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Delete": "删除",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_DeletedSuccessfully": "删除成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry": "删除失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_WaitingForGeneration": "等待生成...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToGenerate": "生成失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_DownloadGif": "下载GIF",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_NoVideoAvailable": "暂无视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_StartCreatingYourFirstAi": "开始创建您的第一个AI视频吧！",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_CreateAVideo": "+ 创建视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToLoad": "加载失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToObtainTheVideo": "获取视频列表失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_Retry": "重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_CreateANewVideo": "+ 创建新视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingVideoList": "正在加载视频列表...",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingMore": "正在加载更多...",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_AllVideosAreDisplayed": "已显示全部视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_SlideDownToLoadMore": "向下滑动加载更多",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationCompleted": "视频生成完成！",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationFailed": "视频生成失败",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationFailedPleaseTry": "视频生成失败，请重试",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationTimedOutPlease": "视频生成超时，请重试",
  "j-dingtalk-web_pages_aiVideo_UnknownStatusResultstatus": "未知状态: {{resultStatus}}",
  "j-dingtalk-web_pages_aiVideo_AnUnknownErrorOccurredWhile": "视频生成遇到未知错误，请重试",
  "j-dingtalk-web_pages_aiVideo_QueryStatusFailed": "查询状态失败",
  "j-dingtalk-web_pages_aiVideo_FailedToQueryTheVideo": "查询视频生成状态失败，请重试",
  "j-dingtalk-web_pages_aiVideo_FailedToObtainTheVideo": "获取视频列表失败",
  "j-dingtalk-web_pages_aiVideo_PleaseUploadAnImage": "请上传图片",
  "j-dingtalk-web_pages_aiVideo_PleaseEnterAForwardDescription": "请输入正向描述",
  "j-dingtalk-web_pages_aiVideo_FailedToGenerate": "生成失败",
  "j-dingtalk-web_pages_aiVideo_FailedToGeneratePleaseTry": "生成失败，请重试",
  "j-dingtalk-web_pages_aiVideo_Loading": "正在加载...",
  "j-dingtalk-web_pages_premiumStore_FailedToLoadStoreData": "店铺数据加载失败~",
  "j-dingtalk-web_utils_download_File": "文件",
  "j-dingtalk-web_utils_download_Video": "视频",
  "j-dingtalk-web_utils_download_DownloadingFiletype": "正在下载{{fileType}}...",
  "j-dingtalk-web_utils_download_FiletypeDownloadedSuccessfully": "{{fileType}}下载成功",
  "j-dingtalk-web_utils_download_FiletypeFailedToDownloadPlease": "{{fileType}}下载失败，请重试",
  "j-dingtalk-web_utils_download_DownloadIsNotSupportedIn": "当前环境不支持下载功能",
  "j-dingtalk-web_utils_download_StartDownloadingFiletype": "开始下载{{fileType}}",
  "j-dingtalk-web_utils_download_FailedToDownloadFiletypePlease": "下载{{fileType}}失败，请重试",
  "j-dingtalk-web_utils_download_FiletypeIsOpenInThe": "{{fileType}}已在浏览器中打开，请手动保存",
  "j-dingtalk-web_utils_download_FiletypeIsOpenInA": "{{fileType}}已在新窗口中打开，请手动保存",
  "j-dingtalk-web_utils_download_SaveAs": "另存为",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif": "转换为GIF",
  "j-dingtalk-web_pages_premiumStore_NumberOfFans": "粉丝数",
  "j-dingtalk-web_pages_premiumStore_PositiveRate": "好评率",
  "j-dingtalk-web_pages_topMerchant_NumberOfProducts": "商品数",
  "j-dingtalk-web_pages_topMerchant_ProductsMentionedInThisVideo": "该视频中提到的商品",
  "j-dingtalk-web_components_InfiniteList_NoDataAvailable": "暂无数据",
  "j-dingtalk-web_components_InfiniteList_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_Today": "今天",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_Yesterday": "昨天",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_DiffdaysDaysAgo": "{{diffDays}}天前",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_MMonthDDay": "M月D日",
  "j-dingtalk-web_pages_my-comment_MyComments": "我的评论",
  "j-dingtalk-web_pages_my-comment_NoCommentRecords": "没有评论记录",
  "j-dingtalk-web_pages_my-comment_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-favorites_components_ProductView_NoItemsForCollection": "暂无收藏的商品",
  "j-dingtalk-web_pages_my-favorites_components_ProductView_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_Contribution": "投稿",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_NoStoreForCollection": "暂无收藏的店铺",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-favorites_components_TrendingView_NoFavoriteProducts": "没有收藏的产品",
  "j-dingtalk-web_pages_my-favorites_components_TrendingView_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-favorites_MyCollection": "我的收藏",
  "j-dingtalk-web_pages_my-favorites_Video": "视频",
  "j-dingtalk-web_pages_my-favorites_Shop": "店铺",
  "j-dingtalk-web_pages_my-favorites_Commodity": "商品",
  "j-dingtalk-web_pages_my-history_components_ProductView_NoBrowsingHistory": "暂无浏览历史",
  "j-dingtalk-web_pages_my-history_components_ProductView_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-history_BrowsingHistory": "浏览历史",
  "j-dingtalk-web_pages_my-history_Video": "视频",
  "j-dingtalk-web_pages_my-history_Commodity": "商品",
  "j-dingtalk-web_pages_my-like_components_ProductCard_FailedToParseProductPayload": "解析 product payload 失败:",
  "j-dingtalk-web_pages_my-like_ILikeIt": "我赞过的",
  "j-dingtalk-web_pages_my-like_IDonTLikeThe": "没有点赞的产品",
  "j-dingtalk-web_pages_my-like_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_work-center_Workbench": "工作台",
  "j-dingtalk-web_pages_work-center_ECommerceApplications": "电商应用",
  "j-dingtalk-web_pages_work-center_ECommerceRelatedApplicationTools": "电商相关的应用工具",
  "j-dingtalk-web_pages_work-center_CommoditySalesStatistics": "商品销售统计表",
  "j-dingtalk-web_pages_work-center_InventoryManagement": "库存管理",
  "j-dingtalk-web_pages_work-center_UserEvaluationAnalysisTable": "用户评价分析表",
  "j-dingtalk-web_pages_work-center_SupplyChainManagement": "供应链管理",
  "j-dingtalk-web_pages_work-center_GeneralApplication": "通用应用",
  "j-dingtalk-web_pages_work-center_GeneralOfficeApplications": "通用办公应用",
  "j-dingtalk-web_pages_work-center_AiTable": "AI表格",
  "j-dingtalk-web_pages_work-center_Document": "文档",
  "j-dingtalk-web_pages_work-center_Schedule": "日程",
  "j-dingtalk-web_pages_work-center_Meeting": "会议",
  "j-dingtalk-web_pages_work-center_FailedToOpenTheLink": "打开链接失败:",
  "j-dingtalk-web_pages_topMerchant_Sold": "已售",
  "j-dingtalk-web_pages_topMerchant_Pieces": "件",
  "j-dingtalk-web_pages_work-center_AiMaterial": "AI 素材",
  "j-dingtalk-web_pages_work-center_EvaluationAndAnalysis": "评价分析",
  "j-dingtalk-web_pages_work-center_EmailAddress": "邮箱"
};