export default {
  "j-agent-web_pages_category_components_CategorySelect_NoCategoryDataIsAvailable": "No category data is available.",
  "j-agent-web_pages_category_LevelCategory": "Level 1 category",
  "j-agent-web_pages_category_ApplyFilterCriteria": "Apply filter criteria:",
  "j-agent-web_pages_category_components_CategorySelect_LevelCategory": "Level 1 category",
  "j-agent-web_pages_category_components_CategorySelect_SecondaryCategory": "Secondary category",
  "j-agent-web_pages_category_components_CategorySelect_LevelCategory_1": "Level 3 category",
  "j-agent-web_pages_category_ClosedSuccessfully": "Closed Successfully",
  "j-agent-web_pages_category_FailedToClose": "Failed to close",
  "j-agent-web_pages_comments_CurrentlyScoringIsNotSupported": "Currently, scoring is not supported.",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CommentsDatacommdesc": "Comments({dataCommDesc})",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentFeatures": "Customer comment features: ",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList": "Best Seller List",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList": "Rising Stars List",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList": "Emerging Potential List",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_SalesList": "Sales Volume List",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_DailySalesList": "Daily Sales List",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_WeeklySalesList": "Weekly Sales List",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_MonthlySalesList": "Monthly Sales List",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_Comments": "Comments",
  "j-agent-web_utils_util_AYearAgo": "A year ago",
  "j-agent-web_utils_util_YearsYearsAgo": "{years} years ago",
  "j-agent-web_utils_util_AMonthAgo": "A month ago",
  "j-agent-web_utils_util_MonthsMonthsAgo": "{months} months ago",
  "j-agent-web_utils_util_ADayAgo": "A day ago",
  "j-agent-web_utils_util_DaysDaysAgo": "{days} days ago",
  "j-agent-web_utils_util_AnHourAgo": "An hour ago",
  "j-agent-web_utils_util_HoursHoursAgo": "{hours} hours ago",
  "j-agent-web_utils_util_OneMinuteAgo": "One minute ago",
  "j-agent-web_utils_util_MinutesMinutesAgo": "{minutes} minutes ago",
  "j-agent-web_utils_util_JustNow": "Just now",
  "j-agent-web_utils_util_SecondsSecondsAgo": "{seconds} seconds ago",
  "j-agent-web_pages_commodityDetail_components_ProductPage_SorryThereIsNoProduct": "Sorry, there is no product data for this list.",
  "j-agent-web_pages_category_SubscriptionSuccessful": "Subscription successful",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadFailed": "{fileName} Upload failed",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadedSuccessfully": "{fileName} uploaded successfully",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFailed": "Upload failed:",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFiles": "Upload files",
  "j-agent-web_pages_agentForm_components_FileUploader_SupportedFormatPhtASingle": "Supported format:.pht. A single file cannot exceed 200MB and can be uploaded at most.",
  "j-agent-web_pages_agentForm_components_FileUploader_Files": "Files",
  "j-agent-web_pages_agentForm_components_FileUploader_PreviewImage": "Preview image",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelId": "Model id",
  "j-agent-web_pages_agentForm_components_FormComponent_Copy": "Copy",
  "j-agent-web_pages_agentForm_components_FormComponent_CopiedSuccessfully": "Copied successfully",
  "j-agent-web_pages_agentForm_components_FormComponent_UpdatedSuccessfully": "Updated successfully",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUploadFailedErr": "Model Upload failed {err}",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionError": "Form submission error:",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionFailedPleaseTry": "Form submission failed. Please try again!",
  "j-agent-web_pages_agentForm_components_FormComponent_TheFormHasBeenReset": "The form has been reset.",
  "j-agent-web_pages_agentForm_components_FormComponent_SelectAModelToUpdate": "Select a model to update",
  "j-agent-web_pages_agentForm_components_FormComponent_TheModelToBeUpdated": "The model to be updated is selected.",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelName": "Model Name",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterAModelName": "Enter a model name",
  "j-agent-web_pages_agentForm_components_FormComponent_UploadModelFiles": "Upload model files",
  "j-agent-web_pages_agentForm_components_FormComponent_PromptEditingSupportsMarkdown": "Prompt editing supports Markdown",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterPrompt": "Enter prompt",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpdate": "Model Update",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpload": "Model Upload",
  "j-agent-web_pages_agentForm_components_FormComponent_Reset": "Reset",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Bold": "Bold",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Italic": "Italic",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_OrderedList": "Ordered list",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_UnorderedList": "Unordered list",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Link": "Link",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Edit": "Edit",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_MarkdownFormatEditingIsSupported": "Markdown format editing is supported. You can enter text, lists, links, etc...",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Preview": "Preview",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_NoContentPreview": "No content preview",
  "j-agent-web_pages_agentForm_AddModel": "Add model",
  "j-agent-web_pages_agentForm_UpdateModelPrompt": "Update model prompt",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainOssConfiguration": "Failed to obtain OSS configuration:",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainUploadCredentials": "Failed to obtain Upload credentials",
  "j-agent-web_pages_agentForm_services_ossService_FailedToUploadTheFile": "Failed to upload the file:",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMost": "The platform sold the most products of all products (or subscribed to certain categories) on the day yesterday;",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostDaily": "The platform sold the most products of all products (or subscribed to certain categories) on the day yesterday;",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostWeekly": "The platform sold the most products of all products (or subscribed to certain categories) in the week;",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostMonthly": "The platform sold the most products of all products (or subscribed to certain categories) in the month;",
  "j-agent-web_pages_cardTips_NewProductsWithTheLargest": "The new products with the most likes launched across all categories (or subscription categories) on this platform in the past 30 days;",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldThe": "The platform yesterday sold the most products (or subscribed to certain categories) on that day compared with the previous day;",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldAll": "The products with the longest ranking days across all categories (or subscription categories) on this platform in the past 30 days.",
  "j-agent-web_pages_cardTips_DataDescription": "Data Description",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_StoreNameProductstorename": "Store: ",
  "j-agent-web_pages_productCuration_components_ProductList_Rating": "Rating:",
  "j-agent-web_pages_productCuration_ProductSelectionCenter": "Product selection center",
  "j-agent-web_pages_productCuration_UnableToObtainBizid": "Unable to obtain bizId",
  "j-agent-web_pages_productCuration_FailedToObtainProductList": "Failed to obtain product list",
  "j-agent-web_pages_productCuration_FailedToSubmitTheProduct": "Failed to submit the product",
  "j-agent-web_pages_productCuration_LoadingGoods": "Loading goods...",
  "j-agent-web_pages_productCuration_ProductslengthItemsInTotalSelectedcount": "{productsLength} items in total, {selectedCount} items have been selected",
  "j-agent-web_pages_productCuration_DeselectAll": "Deselect all",
  "j-agent-web_pages_productCuration_SelectAllCurrentPage": "Select all current page",
  "j-agent-web_pages_productCuration_NoProductDataAvailable": "No product data available",
  "j-agent-web_pages_productCuration_SubmitSelectionSelectedcount": "Submit selection ({selectedCount})",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing": "1688 Product Sourcing",
  "j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData": "Failed to obtain data",
  "j-agent-web_pages_productOrigin_components_ProductPage_ZozoGrid": "ZOZOTOWN Price ",
  "j-agent-web_pages_productOrigin_components_ProductPage_AmazonGrid": "Amazon Price ",
  "j-agent-web_pages_productOrigin_components_ProductPage_RednoteGrid": "Price ",
  "j-agent-web_pages_productOrigin_components_ProductPage_TiktokGrid": "TikTok Price ",
  "j-agent-web_pages_productOrigin_components_ProductPage_TaobaoGrid": "Taobao Price ",
  "j-agent-web_pages_productOrigin_components_ProductPage_AlibabaGrid": "1688Japan Price ",
  "j-agent-web_pages_productOrigin_components_ProductPage_RakutenGrid": "Rakuten Price ",
  "j-agent-web_pages_productOrigin_components_ProductPage_ManualGrid": "Price ",
  "j-agent-web_pages_productOrigin_components_ProductPage_Details": "Details",
  "j-agent-web_pages_productOrigin_components_ProductPage_Favorites": "Favorites",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductdatafavnumPeople": "{productDataFavNum} people",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoCollection": "No collection",
  "j-agent-web_pages_productOrigin_components_ProductPage_List": "List",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductRating": "Product rating",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoScore": "No score",
  "j-agent-web_pages_productOrigin_components_ProductPage_SimilarProducts": "1688 Similar Products",
  "j-agent-web_pages_category_components_CategorySelect_AllCategories": "All categories",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_FindSimilarity": "Find similarity",
  "j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume": "Today's sales volume",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfLikes": "Number of likes",
  "j-agent-web_pages_productOriginV2_components_ProductPage_DailyGrowth": "Daily Growth",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfDaysOnThe": "Number of days on the list",
  "j-agent-web_pages_productOriginV2_components_ProductPage_SalesVolume": "Sales volume",
  "j-agent-web_pages_productOriginV2_components_ProductPage_MonthlySales": "Sales for past 90 days ",
  "j-agent-web_pages_productOriginV2_components_ProductPage_RepurchaseRate": "Repurchase rate ",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ProductEvaluation": "Product Evaluation",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ListSource": "List source",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NoListInformation": "No list information",
  "j-agent-web_pages_productOriginV2_components_ProductPage_FactoryPrice": "Factory Direct Price",
  "j-agent-web_pages_productCuration_components_ProductList_ViewProductDetails": "View Detail",
  "j-agent-web_pages_productOrigin_components_ProductPage_ShareContent": "Looking for 1688japan? Here’s where you’ll find it! Vast selection of Chinese products directly supplied to Japan — lower prices, more choices!",
  "j-agent-web_pages_productCuration_SuccessfullySubmitted": "Successfully submitted {{successItemListLen}} products to Feeds, failed {{failItemListLen}} products",
  "j-dingtalk-web_components_ImageSwiper_ProductPortrait": "Product portrait",
  "j-dingtalk-web_components_OptimizedImage_PortraitFailure": "Portrait failure",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_ForProductInformationSee": "No product found",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pieces": "Pieces",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ProductPortrait": "Product portrait",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NoMore": "No more",
  "j-dingtalk-web_pages_category_FailedToObtainCategoryData": "Failed to obtain category data",
  "j-dingtalk-web_pages_category_SelectASecondaryCategory": "Please select a level-2 category",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_MoreProducts": "More products",
  "j-dingtalk-web_pages_category_CategorySelectionSucceeded": "Subscription successful",
  "j-dingtalk-web_pages_category_CategorySelectionFailed": "Subscription failed, please try again",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_MoreDetails": "More details",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CommentDetails": "Comment details",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentCount": "{{commentCount}} Global Rating",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_StarRating": "{rating} stars",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_ScoreFullScore": "{{score}} Score (Full Score 5)",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CustomerFeedback": "Customer feedback",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_GeneratedByAiBasedOn": "Generated by AI based on customer submission",
  "j-agent-web_pages_productOrigin_components_ProductPage_Price": "Price ",
  "j-dingtalk-web_pages_premiumStore_StoreDetails": "Store Details",
  "j-dingtalk-web_pages_premiumStore_ComprehensiveEvaluation": "Comprehensive evaluation:",
  "j-dingtalk-web_pages_premiumStore_ProductQuality": "Product quality:",
  "j-dingtalk-web_pages_premiumStore_DeliverySpeed": "Delivery speed:",
  "j-dingtalk-web_pages_premiumStore_ConsultingServices": "Consulting services:",
  "j-dingtalk-web_pages_premiumStore_SimilarProducts": "Similar products",
  "j-dingtalk-web_pages_topMerchant_GoodNumberDetails": "Creator Details",
  "j-dingtalk-web_pages_topMerchant_FailedToObtainData": "Failed to obtain data",
  "j-dingtalk-web_pages_topMerchant_Fans": "Fans",
  "j-dingtalk-web_pages_topMerchant_NumberOfInteractions": "Number of interactions",
  "j-dingtalk-web_pages_topMerchant_DataOverview": "Data Overview",
  "j-dingtalk-web_pages_topMerchant_NumberOfLikesReceived": "Number of likes received",
  "j-dingtalk-web_pages_topMerchant_NumberOfNotes": "Number of notes",
  "j-dingtalk-web_pages_topMerchant_NumberOfCooperativeBrands": "Number of cooperative brands",
  "j-dingtalk-web_pages_topMerchant_Mockmerchantinfobrandcount": "{merchantInfoGoodsNum}",
  "j-dingtalk-web_pages_topMerchant_NumberOfConcerns": "Number of concerns",
  "j-dingtalk-web_pages_topMerchant_StyleOverview": "Style overview",
  "j-dingtalk-web_pages_topMerchant_HighestInteraction": "Highest interaction",
  "j-dingtalk-web_pages_topMerchant_RecentlyReleased": "Recently released",
  "j-dingtalk-web_pages_topMerchant_TheGoodsMentionedInThis": "The goods mentioned in this note",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts": "Similar products",
  "j-dingtalk-web_pages_topMerchant_ProductsSoldByTa": "Products sold by TA",
  "j-dingtalk-web_pages_topMerchant_HighestSalesVolume": "Highest sales volume",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts_1": "Similar products",
  "j-dingtalk-web_components_ImageSwiper_LookingForSimilarProducts": "Looking for similar products",
  "j-agent-web_pages_productOrigin_components_ProductPage_CopiedToClipboard": "productId copied",
  "j-dingtalk-web_pages_category_CategorySelect_DailyNewspaper": "Daily report",
  "j-dingtalk-web_pages_category_CategorySelect_WeeklyNewspaper": "Weekly report",
  "j-dingtalk-web_pages_category_CategorySelect_MonthlyReport": "Monthly report",
  "j-dingtalk-web_pages_category_CategorySelect_EveryWorkingDay": "Every working day",
  "j-dingtalk-web_pages_category_CategorySelect_EveryDay": "Every Day",
  "j-dingtalk-web_pages_category_CategorySelect_EveryFriday": "Every Friday",
  "j-dingtalk-web_pages_category_CategorySelect_ThOfEachMonth": "1th of each month",
  "j-dingtalk-web_pages_category_CategorySelect_SubscriptionSettings": "Subscription settings",
  "j-dingtalk-web_pages_category_CategorySelect_SelectDate": "Select date",
  "j-dingtalk-web_pages_category_CategorySelect_SelectTime": "Select time",
  "j-dingtalk-web_pages_category_CategorySelect_Cancel": "Cancel",
  "j-dingtalk-web_pages_category_CategorySelect_Confirm": "Confirm",
  "j-dingtalk-web_pages_category_Comprehensive": "All",
  "j-dingtalk-web_pages_category_PleaseSelectALevelCategory": "Please select a level-1 category",
  "j-dingtalk-web_pages_category_SelectALevelCategory": "Please select a level-3 category",
  "j-dingtalk-web_components_Loading_Loading": "Loading",
  "j-dingtalk-web_pages_category_CategorySelect_Loading": "Loading",
  "j-dingtalk-web_pages_category_CategorySwitchingFailedPleaseTry": "Category switching failed. Please try again.",
  "j-dingtalk-web_pages_premiumStore_MonthlySalesProductmonthsolddisplayPieces": "Monthly sales {productMonthSoldDisplay} pieces",
  "j-dingtalk-web_pages_premiumStore_FailedToObtainStoreData": "Store data loading failed~",
  "j-dingtalk-web_components_SwipeableProductCard_NotInterested": "Not interested",
  "j-dingtalk-web_components_SwipeableProductCard_Interested": "Interested",
  "j-dingtalk-web_components_SwipeableProductCard_Approve": "Approve",
  "j-dingtalk-web_components_SwipeableProductCard_Reject": "Reject",
  "j-dingtalk-web_components_SwipeableProductList_NoProductDataAvailable": "No product data available",
  "j-dingtalk-web_components_SwipeableProductList_SlideLeftAndRightTo": "← slide left and right to switch products →",
  "j-dingtalk-web_components_SwipeableProductList_IMNotInterestedIn": "↑ I'm not interested in sliding up ↓ interested in sliding down",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFakeAndInferiorProducts": "Report fake and inferior products",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportThatThisProductIs": "Report that this product is fake and inferior",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotYet": "Not yet",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ConfirmReport": "Confirm report",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_TheParameterIsIncorrectAnd": "The parameter is incorrect and cannot be reported.",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportSuccessful": "Report successful",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFailedPleaseTryAgain": "Report failed. Please try again later.",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_UnknownError": "Unknown error",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditSuccess": "Audit {{status}}",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditFailed": "Audit failed, please try again later",
  "j-dingtalk-web_pages_sourcing_components_Page_MarkedAsInterested": "Marked as interested",
  "j-dingtalk-web_pages_sourcing_components_Page_MarkedAsNotInterested": "Marked as not interested",
  "j-dingtalk-web_pages_sourcing_components_Page_AuditSuccess": "Audit {{status}} successful",
  "j-dingtalk-web_pages_sourcing_components_Page_AuditFailed": "Audit failed, please try again later",
  "j-dingtalk-web_pages_sourcing_components_Page_SimilarSources": "1688 similar sources",
  "j-dingtalk-web_pages_topMerchant_Favorites": "Favorites",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pass": "YES",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotPassed": "NO",
  "j-dingtalk-web_utils_util_NumberUnitTenThousand": "K",
  "j-dingtalk-web_utils_util_NumberUnitHundredMillion": "M",
  "j-dingtalk-web_pages_topMerchant_LoadMore": "Load More",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_UseThisFeatureInThe": "Use this feature in the 7Ding client",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_InitializingPleaseTryAgainLater": "Initializing. Please try again later.",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully": "Image uploaded successfully",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed": "Image Upload failed",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailedPleaseTry": "Image Upload failed. Please try again.",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage": "Click upload image",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading": "Uploading...",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageSizeError": "Image size does not meet requirements. Required: min {minWidth}x{minHeight}, max {maxWidth}x{maxHeight}.",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageLoadError": "Failed to load image, please try again",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ImageSpecification": "Image specification",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_MaterialType": "Material Type:",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheElementsAreSimpleThe": "1. The elements are simple, the light is sufficient, the product is clear, and the outline is obvious.",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ItIsRecommendedToUpload": "2. It is recommended to upload a model picture or a real person to display the Product Picture (half-length and close-range effect are better)",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Size": "Size:",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_SupportsJpgPngFormatWith": "1. Supports JPG/PNG format with a size of no more than 5m",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheShortSideIsNot": "2. The short side is not less than 300PX, and the aspect ratio is between 5:2 and 2:5.",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CorrectExample": "Correct example",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_HandheldProductWithClearText": "Handheld product with clear text",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheProductIsClearAnd": "The product is clear and the outline is obvious",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelHalfLengthDisplayClothing": "Model half-length display clothing",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelTrialProducts": "Model trial products",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ErrorExample": "Error example",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_StitchingPicture": "Stitching picture",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_UnclearSubject": "Unclear subject",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CreativeDescriptionReference": "Creative description reference",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_DescribeThePictureAndAction": "Describe the picture and action you want to generate in combination with the picture; It is recommended to use the description method of \"subject + action\", such as \"model smiling forward\"",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ReferenceExample": "Reference example",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_MenSlowlyPickUpThe": "Men slowly pick up the wine glass",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptMenSlowlyPickUp": "Prompt: men slowly pick up wine glasses",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheLensSlowlyRotatesTo": "The lens slowly rotates to the right to show the game host chassis",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptTheLensSlowlyRotates": "Prompt: the lens slowly rotates to the right, showing game host chassis",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelPosingShowingYogaClothes": "Model posing, showing yoga clothes",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptModelPosingShowingYoga": "Prompt: Model posing, showing yoga clothes",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ContentYouDonTWant": "Content you don't want to appear (not required)",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CopiedToCreativeDescription": "Copied to creative description",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription": "Copy to creative description",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseUploadAnImage": "Please upload an image",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseEnterAForwardDescription": "Please enter a forward description",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Hd": "HD",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_StandardClear": "Standard clear",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_FirstFrameVideoPicture": "First Frame video picture",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_YourCreativeDescription": "Your creative description",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsModelDisplay": "Input such as \"model display products\"",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsDistortionDistortion": "Input such as distortion, distortion, deformation, low quality, etc.",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDefinition": "Video definition",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDuration": "Video duration",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Generating": "Generating...",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_GenerateNow": "Generate now",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_HelpInformation": "Help information",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Generating": "Generating...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ItIsExpectedToBe": "It is expected to be completed in 1 to 3 minutes, and you can exit during this period.",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Hd": "HD",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_StandardClear": "Standard clear",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_PleaseClickTheFullScreen": "Please click the full screen button in the lower right corner of the video.",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ThePraiseIsCanceled": "The praise is canceled.",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ThumbsUpSuccessfully": "Thumbs up successfully",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry": "The operation failed. Please try again.",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_TheCancellationPointIsStepped": "The cancellation point is stepped on successfully.",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ClickSuccessfully": "Click successfully",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifAlreadyExistsStartDownloading": "GIF already exists, start downloading...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationCompleted": "GIF generation completed!",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedToStart": "GIF generation failed to start",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationHasStartedPlease": "GIF generation has started. Please wait...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedPleaseTry": "GIF generation failed. Please try again.",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailed": "GIF generation failed",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationTimeout": "GIF generation timeout",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_UnknownStatusResultstatus": "Unknown status: {resultStatus}",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegeneratingVideo": "Regenerating video...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationHasStartedPleaseWait": "Regeneration has started. Please wait...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailed": "Regeneration failed",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailedPleaseTryAgain": "Regeneration failed. Please try again.",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ConfirmDeletion": "Confirm deletion",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_AreYouSureYouWant": "Are you sure you want to delete this video?",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Cancel": "Cancel",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Delete": "Delete",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_DeletedSuccessfully": "Deleted successfully",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry": "Failed to delete. Please try again.",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_WaitingForGeneration": "Waiting for generation...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToGenerate": "Failed to generate",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_DownloadGif": "Download GIF",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_NoVideoAvailable": "No video available",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_StartCreatingYourFirstAi": "Start creating your first AI video!",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_CreateAVideo": "+ create a video",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToLoad": "Failed to load",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToObtainTheVideo": "Failed to obtain the video list. Please try again.",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_Retry": "Retry",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_CreateANewVideo": "+ create a new video",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingVideoList": "Loading video list...",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingMore": "Loading more...",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_AllVideosAreDisplayed": "All videos are displayed",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_SlideDownToLoadMore": "Slide down to load more",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationCompleted": "Video Generation completed!",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationFailed": "Video generation failed",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationFailedPleaseTry": "Video generation failed. Please try again.",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationTimedOutPlease": "Video generation timed out. Please try again.",
  "j-dingtalk-web_pages_aiVideo_UnknownStatusResultstatus": "Unknown status: {resultStatus}",
  "j-dingtalk-web_pages_aiVideo_AnUnknownErrorOccurredWhile": "An unknown error occurred while generating the video. Please try again.",
  "j-dingtalk-web_pages_aiVideo_QueryStatusFailed": "Query status failed",
  "j-dingtalk-web_pages_aiVideo_FailedToQueryTheVideo": "Failed to query the video generation status. Please try again.",
  "j-dingtalk-web_pages_aiVideo_FailedToObtainTheVideo": "Failed to obtain the video list",
  "j-dingtalk-web_pages_aiVideo_PleaseUploadAnImage": "Please upload an image",
  "j-dingtalk-web_pages_aiVideo_PleaseEnterAForwardDescription": "Please enter a forward description",
  "j-dingtalk-web_pages_aiVideo_FailedToGenerate": "Failed to generate",
  "j-dingtalk-web_pages_aiVideo_FailedToGeneratePleaseTry": "Failed to generate. Please try again.",
  "j-dingtalk-web_pages_aiVideo_Loading": "Loading...",
  "j-dingtalk-web_pages_premiumStore_FailedToLoadStoreData": "Failed to load store data ~",
  "j-dingtalk-web_utils_download_File": "File",
  "j-dingtalk-web_utils_download_Video": "Video",
  "j-dingtalk-web_utils_download_DownloadingFiletype": "Downloading {fileType}...",
  "j-dingtalk-web_utils_download_FiletypeDownloadedSuccessfully": "{fileType} downloaded successfully",
  "j-dingtalk-web_utils_download_FiletypeFailedToDownloadPlease": "{fileType} failed to download. Please try again.",
  "j-dingtalk-web_utils_download_DownloadIsNotSupportedIn": "Download is not supported in the current environment.",
  "j-dingtalk-web_utils_download_StartDownloadingFiletype": "Start downloading {fileType}",
  "j-dingtalk-web_utils_download_FailedToDownloadFiletypePlease": "Failed to download {fileType}. Please try again.",
  "j-dingtalk-web_utils_download_FiletypeIsOpenInThe": "{fileType} is open in the browser. Please save it manually.",
  "j-dingtalk-web_utils_download_FiletypeIsOpenInA": "{fileType} is open in a new window. Please save it manually.",
  "j-dingtalk-web_utils_download_SaveAs": "Save as",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif": "Converting to GIF",
  "j-dingtalk-web_pages_premiumStore_NumberOfFans": "Number of fans",
  "j-dingtalk-web_pages_premiumStore_PositiveRate": "Positive rate",
  "j-dingtalk-web_pages_topMerchant_NumberOfProducts": "Number of Products",
  "j-dingtalk-web_pages_topMerchant_ProductsMentionedInThisVideo": "Products mentioned in this video",
  "j-dingtalk-web_components_InfiniteList_NoDataAvailable": "No data available",
  "j-dingtalk-web_components_InfiniteList_NetworkErrorPleaseTryAgain": "Network error, please try again later",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_Today": "Today",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_Yesterday": "Yesterday",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_DiffdaysDaysAgo": "{diffDays} days ago",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_MMonthDDay": "M month D Day",
  "j-dingtalk-web_pages_my-comment_MyComments": "My comments",
  "j-dingtalk-web_pages_my-comment_NoCommentRecords": "No comment Records",
  "j-dingtalk-web_pages_my-comment_NetworkErrorPleaseTryAgain": "Network error, please try again later",
  "j-dingtalk-web_pages_my-favorites_components_ProductView_NoItemsForCollection": "No items for collection",
  "j-dingtalk-web_pages_my-favorites_components_ProductView_NetworkErrorPleaseTryAgain": "Network error, please try again later",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_Contribution": "Contribution",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_NoStoreForCollection": "No store for collection",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_NetworkErrorPleaseTryAgain": "Network error, please try again later",
  "j-dingtalk-web_pages_my-favorites_components_TrendingView_NoFavoriteProducts": "No favorite products",
  "j-dingtalk-web_pages_my-favorites_components_TrendingView_NetworkErrorPleaseTryAgain": "Network error, please try again later",
  "j-dingtalk-web_pages_my-favorites_MyCollection": "My collection",
  "j-dingtalk-web_pages_my-favorites_Video": "Video",
  "j-dingtalk-web_pages_my-favorites_Shop": "Shop",
  "j-dingtalk-web_pages_my-favorites_Commodity": "Commodity",
  "j-dingtalk-web_pages_my-history_components_ProductView_NoBrowsingHistory": "No browsing history",
  "j-dingtalk-web_pages_my-history_components_ProductView_NetworkErrorPleaseTryAgain": "Network error, please try again later",
  "j-dingtalk-web_pages_my-history_BrowsingHistory": "Browsing History",
  "j-dingtalk-web_pages_my-history_Video": "Video",
  "j-dingtalk-web_pages_my-history_Commodity": "Commodity",
  "j-dingtalk-web_pages_my-like_components_ProductCard_FailedToParseProductPayload": "Failed to parse product payload:",
  "j-dingtalk-web_pages_my-like_ILikeIt": "I like it",
  "j-dingtalk-web_pages_my-like_IDonTLikeThe": "I don't like the product",
  "j-dingtalk-web_pages_my-like_NetworkErrorPleaseTryAgain": "Network error, please try again later",
  "j-dingtalk-web_pages_work-center_Workbench": "Workbench",
  "j-dingtalk-web_pages_work-center_ECommerceApplications": "E-commerce applications",
  "j-dingtalk-web_pages_work-center_ECommerceRelatedApplicationTools": "E-commerce related application tools",
  "j-dingtalk-web_pages_work-center_CommoditySalesStatistics": "Commodity sales statistics",
  "j-dingtalk-web_pages_work-center_InventoryManagement": "Inventory management",
  "j-dingtalk-web_pages_work-center_UserEvaluationAnalysisTable": "User Evaluation Analysis Table",
  "j-dingtalk-web_pages_work-center_SupplyChainManagement": "Supply Chain Management",
  "j-dingtalk-web_pages_work-center_GeneralApplication": "General application",
  "j-dingtalk-web_pages_work-center_GeneralOfficeApplications": "General Office applications",
  "j-dingtalk-web_pages_work-center_AiTable": "AI table",
  "j-dingtalk-web_pages_work-center_Document": "Document",
  "j-dingtalk-web_pages_work-center_Schedule": "Schedule",
  "j-dingtalk-web_pages_work-center_Meeting": "Meeting",
  "j-dingtalk-web_pages_work-center_FailedToOpenTheLink": "Failed to open the link:",
  "j-dingtalk-web_pages_topMerchant_Sold": "Sold",
  "j-dingtalk-web_pages_topMerchant_Pieces": "Pieces",
  "j-dingtalk-web_pages_work-center_AiMaterial": "AI material",
  "j-dingtalk-web_pages_work-center_EvaluationAndAnalysis": "Evaluation and Analysis",
  "j-dingtalk-web_pages_work-center_EmailAddress": "Email address"
};