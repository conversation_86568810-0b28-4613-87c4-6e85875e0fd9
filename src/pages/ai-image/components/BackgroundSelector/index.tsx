import React, { useRef, useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button } from 'dingtalk-design-mobile';
import { Popover } from 'dingtalk-design-desktop';
import { BackgroundOption } from '@/common/types';
import { isMobileDevice } from '@/utils/jsapi';
import './index.less';

interface BackgroundSelectorProps {
  onChange: (background: BackgroundOption | null) => void;
  disabled?: boolean;
}

const BackgroundSelector: React.FC<BackgroundSelectorProps> = ({
  onChange,
  disabled = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<BackgroundOption | null>(null);
  const [showModal, setShowModal] = useState(false);
  const isMobile = isMobileDevice();

  // Windows端鼠标滚轮横向滚动支持 - Windows mouse wheel horizontal scrolling support
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleWheel = (e: WheelEvent) => {
      // 检测是否为垂直滚动 - Check if it's vertical scrolling
      if (Math.abs(e.deltaY) > Math.abs(e.deltaX)) {
        e.preventDefault(); // 阻止默认垂直滚动 - Prevent default vertical scrolling

        // 将垂直滚动转换为横向滚动 - Convert vertical scroll to horizontal scroll
        const scrollAmount = e.deltaY * 0.5; // 调整滚动速度 - Adjust scroll speed
        container.scrollLeft += scrollAmount;
      }
    };

    // 添加滚轮事件监听 - Add wheel event listener
    container.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      container.removeEventListener('wheel', handleWheel);
    };
  }, []);

  // 纹理背景选项数据 - Texture background options data
  const backgroundOptions: BackgroundOption[] = [
    {
      id: 'pic1',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01VWnU5l1VkWtjSufLQ_!!6000000002691-2-tps-216-216.png',
      title: '科幻之光',
      desc: '黑色玻璃展台，球体和台阶',
    },
    {
      id: 'pic2',
      imageUrl: 'https://img.alicdn.com/imgextra/i1/O1CN01TJzn7B21yKXKtYEyG_!!6000000007053-2-tps-216-216.png',
      title: '暗台灯条',
      desc: '金属台面，垂直 LED 灯带发出明亮',
    },
    {
      id: 'pic3',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01qBBzTw1P4xFYea5J0_!!6000000001788-2-tps-216-216.png',
      title: '霓虹灯火',
      desc: '雨后沥青路、倒影、迷离、颜色、城市灯光',
    },
    {
      id: 'pic4',
      imageUrl: 'https://img.alicdn.com/imgextra/i2/O1CN01KYVxh21Q74hRdAnY5_!!6000000001928-2-tps-216-216.png',
      title: '明亮木台',
      desc: '木质书桌，方形和台阶，亮色调',
    },
    {
      id: 'pic5',
      imageUrl: 'https://img.alicdn.com/imgextra/i3/O1CN01poht3I1RnV0D8S3gR_!!6000000002156-2-tps-216-216.png',
      title: '蓝色展台',
      desc: '展台、金色、蓝色、高级',
    },
    {
      id: 'pic6',
      imageUrl: 'https://img.alicdn.com/imgextra/i1/O1CN01RpVT781iLQBpuUoET_!!6000000004396-2-tps-216-216.png',
      title: '镶金岩石',
      desc: '金属台面深色纹理岩石，以黑色为主略带金色点缀，垂直 LED 灯带发出明亮',
    },
    {
      id: 'pic7',
      imageUrl: 'https://img.alicdn.com/imgextra/i4/O1CN01wDB7D01FkV1FxRge0_!!6000000000525-2-tps-216-216.png',
      title: '白色空间',
      desc: '白色空间、背景光效、明亮环境',
    },
  ];

  // Handle template click - 处理模板点击
  const handleTemplateClick = (option: BackgroundOption) => {
    if (disabled) return;

    if (isMobile) {
      // Mobile: Show modal - 移动端：显示模态弹层
      setSelectedTemplate(option);
      setShowModal(true);
    } else {
      // PC: Direct fill (handled by popover button) - PC端：直接填入（由popover按钮处理）
      // This will be handled by the popover's fill button
    }
  };

  // Handle fill in action - 处理填入操作
  const handleFillIn = (option: BackgroundOption) => {
    onChange(option);
    if (isMobile) {
      setShowModal(false);
    }
  };

  // Handle cancel action - 处理取消操作
  const handleCancel = () => {
    setShowModal(false);
    setSelectedTemplate(null);
  };

  // Render background preview - 渲染背景预览
  const renderBackgroundPreview = (option: BackgroundOption) => {
    const backgroundElement = (
      <div
        key={option.id}
        className={`background-option ${disabled ? 'disabled' : ''}`}
        onClick={() => handleTemplateClick(option)}
      >
        <div
          className="background-preview"
          style={{
            backgroundImage: `url(${option.imageUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      </div>
    );

    // PC端：使用 Popover 悬停显示详情 - PC: Use Popover for hover details
    if (!isMobile && !disabled) {
      const popoverContent = (
        <div className="background-popover-content">
          <div className="popover-layout">
            <div className="popover-image">
              <img
                src={option.imageUrl}
                alt={option.title}
                className="popover-preview-image"
              />
            </div>
            <div className="popover-info">
              <h4 className="popover-title">{option.title}</h4>
              <p className="popover-desc">{option.desc}</p>
              <Button
                type="primary"
                size="small"
                className="popover-fill-btn"
                onClick={() => handleFillIn(option)}
              >
                填入
              </Button>
            </div>
          </div>
        </div>
      );

      return (
        <Popover
          content={popoverContent}
          placement="bottom"
          trigger="hover"
          overlayClassName="background-template-popover"
        >
          {backgroundElement}
        </Popover>
      );
    }

    // 移动端或禁用状态：直接返回背景元素 - Mobile or disabled: return background element directly
    return backgroundElement;
  };

  return (
    <>
      <div
        ref={containerRef}
        className={`background-selector ${disabled ? 'disabled' : ''}`}
      >
        {/* 纹理背景选项 - Texture background options */}
        {backgroundOptions.map(renderBackgroundPreview)}
      </div>

      {/* Mobile Modal - 移动端模态弹层 */}
      {isMobile && selectedTemplate && (
        <Modal
          visible={showModal}
          onClose={handleCancel}
          className="background-template-modal"
          maskClosable
        >
          <div className="modal-content">
            <div className="modal-image">
              <img
                src={selectedTemplate.imageUrl}
                alt={selectedTemplate.title}
                className="modal-preview-image"
              />
            </div>
            <div className="modal-info">
              <h3 className="modal-title">{selectedTemplate.title}</h3>
              <p className="modal-desc">{selectedTemplate.desc}</p>
            </div>
            <div className="modal-actions">
              <Button
                className="modal-cancel-btn"
                onClick={handleCancel}
              >
                取消
              </Button>
              <Button
                type="primary"
                className="modal-fill-btn"
                onClick={() => handleFillIn(selectedTemplate)}
              >
                填入
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default BackgroundSelector;
