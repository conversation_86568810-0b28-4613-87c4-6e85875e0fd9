import React from 'react';
import { Button } from 'dingtalk-design-mobile';
import { PictureOutlined } from '@ali/ding-icons';
import './index.less';

interface WelcomeScreenProps {
  hasGenerateBtn: boolean;
  onGetStarted: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ hasGenerateBtn, onGetStarted }) => {
  return (
    <div className="ai-image-welcome-screen">
      <div className="welcome-content">
        <div className="logo-section">
          <PictureOutlined className="logo-image" />
          <h1 className="title">AI画像生成</h1>
          <p className="subtitle">
            背景替换和自动裁剪功能为您<br />
            生成个性化高清图片
          </p>
        </div>

        {hasGenerateBtn && (
          <Button
            type="primary"
            size="large"
            className="get-started-btn"
            onClick={onGetStarted}
          >
            开始创作
          </Button>
        )}
      </div>
    </div>
  );
};

export default WelcomeScreen;
