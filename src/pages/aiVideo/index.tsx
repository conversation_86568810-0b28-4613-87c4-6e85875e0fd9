import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useState, useRef } from 'react';
import { Toast } from 'dingtalk-design-mobile';
import theme, { IThemeType } from 'dingtalk-theme';
import { isDingTalk, isPc, isMobileDevice, setPageTitle } from '@/utils/jsapi';
import { sendUT } from '@/utils/trace';
import { generateVideo, checkVideoStatus, listVideos } from '@/apis';
import $setRight from '@ali/dingtalk-jsapi/api/biz/navigation/setRight';
import $setShare from '@ali/dingtalk-jsapi/api/biz/util/share';
import Loading from '@/components/Loading';
import WelcomeScreen from './components/WelcomeScreen';
import VideoForm from './components/VideoForm';
import VideoList from './components/VideoList';
import './index.less';

theme.setTheme(IThemeType.dark);

// Video information interface
interface VideoInfo {
  uuid: string;
  videoUrl: string;
  imageUrl: string;
  gifUrl?: string;
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  duration: number;
  userRating?: string;
  videoFinishTime?: string;
  requestId?: string;
  createdAt?: string; // Video creation timestamp for progress calculation
  loadVideoList: () => void;
  status: 'pending' | 'processing' | 'finish' | 'failed';
}

interface AIVideoState {
  currentStep: 'welcome' | 'form' | 'videoList';
  rightPanelContent: 'welcome' | 'videoList'; // For PC layout right panel
  uploadedImage: string | null;
  positivePrompt: string;
  negativePrompt: string;
  quality: string;
  isGenerating: boolean;
  generatedVideoUrl: string | null;
  error: string | null;
  taskId: string | null;
  progress: number;
  // Video list related states
  videoList: VideoInfo[];
  isLoadingVideos: boolean;
  hasVideos: boolean;
  videoListError: string | null;
  nextUuid: string | null;
  hasNextPage: boolean;
  progressMap: Record<string, number>; // Map of video UUID to progress percentage
  isLoadingMore: boolean; // Loading state for pagination
}

const AIVideoPage: React.FC = () => {
  const [state, setState] = useState<AIVideoState>({
    currentStep: isMobileDevice() ? 'welcome' : 'form',
    rightPanelContent: 'welcome', // PC layout right panel starts with welcome
    uploadedImage: null,
    positivePrompt: '',
    negativePrompt: '',
    quality: 'high',
    isGenerating: false,
    generatedVideoUrl: null,
    error: null,
    taskId: '',
    progress: 0,
    // Video list related states
    videoList: [],
    isLoadingVideos: true, // Start with loading state
    hasVideos: false,
    videoListError: null,
    nextUuid: null,
    hasNextPage: false,
    progressMap: {},
    isLoadingMore: false, // Loading state for pagination
  });

  // Use ref to store polling timers to avoid re-renders and ensure cleanup
  const pollingTimersRef = useRef<Record<string, ReturnType<typeof setTimeout>>>({});

  // Calculate progress based on time elapsed since video creation
  const calculateTimeBasedProgress = (createdAt: string): number => {
    try {
      const createdTime = new Date(createdAt).getTime();
      const currentTime = Date.now();
      const elapsedSeconds = (currentTime - createdTime) / 1000;

      // Estimated total generation time: 300 seconds (5 minutes)
      const estimatedTotalSeconds = 300;

      // Calculate progress percentage
      let progress = elapsedSeconds / estimatedTotalSeconds * 100;

      // Ensure progress is between 2% and 95%
      progress = Math.max(2, Math.min(95, progress));

      return Math.round(progress);
    } catch (error) {
      // If timestamp parsing fails, return minimum progress
      return 2;
    }
  };

  // Utility function to scroll to top of video list
  const scrollToTop = () => {
    setTimeout(() => {
      if (isMobileDevice()) {
        // Mobile: scroll window to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        // PC: scroll right panel to top
        const rightPanel = document.querySelector('.ai-video-right-panel') as HTMLElement;
        if (rightPanel) {
          rightPanel.scrollTo({ top: 0, behavior: 'smooth' });
        }
      }
    }, 100); // Small delay to ensure DOM is updated
  };

  useEffect(() => {
    // Set page title
    setPageTitle('AI视频生成');
    // Set share button
    setShare();
    // Send page view event
    sendUT('ai_video_page_view', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });
    // Load video list on page initialization
    loadVideoList();

    // Cleanup all polling timers when component unmounts
    return () => {
      Object.values(pollingTimersRef.current).forEach((timer) => {
        clearTimeout(timer);
      });
      pollingTimersRef.current = {};
    };
  }, []);

  const setShare = () => {
    if (!isDingTalk() || isPc) {
      return;
    }

    $setRight({
      show: true,
      control: true,
      text: '•••',
      onSuccess: () => {
        $setShare({
          type: 0,
          url: window.location.href,
          title: 'AI视频生成',
          content: '制作高质量的电商产品视频从未如此简单。',
          image: 'https://img.alicdn.com/imgextra/i1/O1CN01DfZoSi1cL4BQtyhAm_!!6000000003583-2-tps-192-192.png',
        });
      },
    });
  };

  // Stop polling for a specific video
  const stopPollingForVideo = (uuid: string) => {
    if (pollingTimersRef.current[uuid]) {
      clearTimeout(pollingTimersRef.current[uuid]);
      delete pollingTimersRef.current[uuid];
    }
  };

  // Generic polling function for video status
  const pollVideoStatus = async (
    uuid: string,
    pollCount = 0,
    isCurrentlyGenerating = false,
  ): Promise<void> => {
    const maxPollCount = 60; // Maximum 60 polls (5 minutes total)
    const pollInterval = 5000; // 5 seconds interval

    try {
      const currentPollCount = pollCount + 1;

      // Calculate progress: use time-based progress if available, otherwise use poll-based
      let progressPercentage: number;

      setState((prev) => {
        // Check if we already have a time-based progress for this video
        const existingProgress = prev.progressMap[uuid];

        if (existingProgress && existingProgress > 2) {
          // Use existing time-based progress and increment slightly
          progressPercentage = Math.min(existingProgress + 1, 95);
        } else {
          // Fallback to poll-based progress calculation
          progressPercentage = Math.min(currentPollCount / maxPollCount * 90, 90);
        }

        return {
          ...prev,
          progress: isCurrentlyGenerating ? progressPercentage : prev.progress,
          progressMap: {
            ...prev.progressMap,
            [uuid]: progressPercentage,
          },
        };
      });

      const result = await checkVideoStatus({ uuid });

      // Check if status is success with videoUrl
      if (result.status === 'finish' && result.videoUrl) {
        stopPollingForVideo(uuid);
        setState((prev) => {
          const newProgressMap = { ...prev.progressMap };
          delete newProgressMap[uuid];
          return {
            ...prev,
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            generatedVideoUrl: isCurrentlyGenerating ? result.videoUrl : prev.generatedVideoUrl,
            progress: isCurrentlyGenerating ? 100 : prev.progress,
            progressMap: newProgressMap,
            // Update the video in the list
            videoList: prev.videoList.map((video) =>
              (video.uuid === uuid ?
                {
                  ...video,
                  videoUrl: result.videoUrl,
                  videoFinishTime: result.finishTime,
                  duration: result.duration || video.duration || 5,
                  status: 'finish' as const,
                } :
                video)),
          };
        });
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationCompleted'),
          position: 'top',
          maskClickable: true,
          duration: 2,
        });
        return;
      }

      // Check if status is failed
      if (result.status === 'failed') {
        stopPollingForVideo(uuid);
        setState((prev) => {
          const newProgressMap = { ...prev.progressMap };
          delete newProgressMap[uuid];
          return {
            ...prev,
            isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
            error: isCurrentlyGenerating ? i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationFailed') : prev.error,
            progress: isCurrentlyGenerating ? 0 : prev.progress,
            progressMap: newProgressMap,
            // Update the video status in the list
            videoList: prev.videoList.map((video) =>
              (video.uuid === uuid ?
                { ...video, status: 'failed' as const } :
                video)),
          };
        });
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationFailedPleaseTry'),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
        return;
      }

      // If status is pending or processing, continue polling
      if (result.status === 'pending' || result.status === 'processing') {
        // Check if we've reached the maximum poll count
        if (currentPollCount >= maxPollCount) {
          stopPollingForVideo(uuid);
          setState((prev) => {
            const newProgressMap = { ...prev.progressMap };
            delete newProgressMap[uuid];
            return {
              ...prev,
              isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
              error: isCurrentlyGenerating ? i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationTimedOutPlease') : prev.error,
              progress: isCurrentlyGenerating ? 0 : prev.progress,
              progressMap: newProgressMap,
              // Update the video status in the list
              videoList: prev.videoList.map((video) =>
                (video.uuid === uuid ?
                  { ...video, status: 'failed' as const } :
                  video)),
            };
          });
          Toast.fail({
            content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationTimedOutPlease'),
            position: 'top',
            maskClickable: true,
            duration: 3,
          });
          return;
        }

        // Continue polling after interval
        const timer = setTimeout(() => {
          pollVideoStatus(uuid, currentPollCount, isCurrentlyGenerating);
        }, pollInterval);
        pollingTimersRef.current[uuid] = timer;
        return;
      }

      // Handle unexpected status
      stopPollingForVideo(uuid);
      setState((prev) => {
        const newProgressMap = { ...prev.progressMap };
        delete newProgressMap[uuid];
        return {
          ...prev,
          isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
          error: isCurrentlyGenerating ? i18next.t('j-dingtalk-web_pages_aiVideo_UnknownStatusResultstatus', { resultStatus: result.status }) : prev.error,
          progress: isCurrentlyGenerating ? 0 : prev.progress,
          progressMap: newProgressMap,
          // Update the video status in the list
          videoList: prev.videoList.map((video) =>
            (video.uuid === uuid ?
              { ...video, status: 'failed' as const } :
              video)),
        };
      });
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_AnUnknownErrorOccurredWhile'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    } catch (error) {
      stopPollingForVideo(uuid);
      setState((prev) => {
        const newProgressMap = { ...prev.progressMap };
        delete newProgressMap[uuid];
        const errorMessage = error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_aiVideo_QueryStatusFailed');
        return {
          ...prev,
          isGenerating: isCurrentlyGenerating ? false : prev.isGenerating,
          error: isCurrentlyGenerating ? errorMessage : prev.error,
          progress: isCurrentlyGenerating ? 0 : prev.progress,
          progressMap: newProgressMap,
          // Update the video status in the list
          videoList: prev.videoList.map((video) =>
            (video.uuid === uuid ?
              { ...video, status: 'failed' as const } :
              video)),
        };
      });
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_FailedToQueryTheVideo'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Start polling for a specific video
  const startPollingForVideo = (uuid: string, isCurrentlyGenerating = false) => {
    // Don't start polling if already polling this video
    if (pollingTimersRef.current[uuid]) {
      return;
    }
    pollVideoStatus(uuid, 0, isCurrentlyGenerating);
  };

  // Check video list and start polling for videos that need it
  const checkAndStartPollingForVideos = (videoList: VideoInfo[]) => {
    videoList.forEach((video) => {
      if ((video.status === 'pending' || video.status === 'processing') && !pollingTimersRef.current[video.uuid]) {
        startPollingForVideo(video.uuid);
      }
    });
  };

  // Load video list from API
  const loadVideoList = async () => {
    setState((prev) => ({ ...prev, isLoadingVideos: true, videoListError: null }));

    try {
      let result;
      try {
        result = await listVideos({ limit: 20 });
      } catch (error) {
        // Handle API error silently, log for debugging if needed
        console.error('Video list API error:', error);
      }

      if (result?.videoInfos?.length > 0) {
        const videoInfos = JSON.parse(result.videoInfos) || [];
        // Transform API data to match VideoInfo interface
        const videoList: VideoInfo[] = videoInfos.map((item: any) => {
          // Parse refImageInfo JSON string to get imageUrl
          let imageUrl = '';
          try {
            const refImageInfo = JSON.parse(item.refImageInfo || '{}');
            imageUrl = refImageInfo.url || '';
          } catch (e) {
            imageUrl = '';
          }

          return {
            uuid: item.uuid,
            videoUrl: item.videoUrl || '', // May be empty for processing videos
            gifUrl: item.gifUrl || '', // May be empty if GIF hasn't been generated
            imageUrl,
            positivePrompt: item.requirements || '',
            negativePrompt: item.restrictions || '',
            quality: item.quality,
            duration: item.duration || 5, // Default to 5 seconds if not provided
            userRating: item.userRating || '0',
            videoFinishTime: item.videoFinishTime,
            status: item.status,
            requestId: item.requestId,
            createdAt: item.createdAt, // Add creation timestamp
          };
        });

        const hasVideos = videoList.length > 0;
        const isMobile = isMobileDevice();

        // Calculate initial progress for processing videos based on creation time
        const initialProgressMap: Record<string, number> = {};
        videoList.forEach((video) => {
          if (video.status === 'processing' && video.createdAt) {
            initialProgressMap[video.uuid] = calculateTimeBasedProgress(video.createdAt);
          }
        });

        // Determine initial step based on device and video availability
        let initialStep: AIVideoState['currentStep'];
        if (isMobile) {
          initialStep = hasVideos ? 'videoList' : 'welcome';
        } else {
          initialStep = 'form';
        }

        setState((prev) => ({
          ...prev,
          videoList,
          hasVideos,
          isLoadingVideos: false,
          nextUuid: result.nextUuid || null,
          hasNextPage: result.hasNext || false,
          // Initialize progress map with time-based progress for processing videos
          progressMap: { ...prev.progressMap, ...initialProgressMap },
          // Update initial display logic based on video list
          currentStep: initialStep,
          rightPanelContent: hasVideos ? 'videoList' : 'welcome',
        }));

        // Scroll to top when switching to video list view
        if (hasVideos) {
          scrollToTop();
        }

        // Start polling for videos that need it
        checkAndStartPollingForVideos(videoList);
      } else {
        // Handle empty video list
        setState((prev) => ({
          ...prev,
          videoList: [],
          hasVideos: false,
          isLoadingVideos: false,
          nextUuid: null,
          hasNextPage: false,
          currentStep: isMobileDevice() ? 'welcome' : 'form',
          rightPanelContent: 'welcome',
        }));
      }
    } catch (error) {
      // Handle API error silently, log for debugging if needed
      setState((prev) => ({
        ...prev,
        isLoadingVideos: false,
        videoListError: error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_aiVideo_FailedToObtainTheVideo'),
        hasVideos: false,
        // Keep original welcome/form logic when API fails
        currentStep: isMobileDevice() ? 'welcome' : 'form',
        rightPanelContent: 'welcome',
      }));
    }
  };

  // Load more videos for pagination
  const loadMoreVideos = async () => {
    // Don't load if already loading or no more pages
    if (state.isLoadingMore || !state.hasNextPage || !state.nextUuid) {
      return;
    }

    setState((prev) => ({ ...prev, isLoadingMore: true }));

    try {
      const result = await listVideos({
        limit: 20, // Use smaller limit for pagination
        nextUuid: state.nextUuid,
      });

      if (result?.videoInfos?.length > 0) {
        const videoInfos = JSON.parse(result.videoInfos) || [];
        // Transform API data to match VideoInfo interface
        const newVideoList: VideoInfo[] = videoInfos.map((item: any) => {
          // Parse refImageInfo JSON string to get imageUrl
          let imageUrl = '';
          try {
            const refImageInfo = JSON.parse(item.refImageInfo || '{}');
            imageUrl = refImageInfo.url || '';
          } catch (e) {
            imageUrl = '';
          }

          return {
            uuid: item.uuid,
            videoUrl: item.videoUrl || '', // May be empty for processing videos
            gifUrl: item.gifUrl || '', // May be empty if GIF hasn't been generated
            imageUrl,
            positivePrompt: item.requirements || '',
            negativePrompt: item.restrictions || '',
            quality: item.quality,
            duration: item.duration || 5, // Default to 5 seconds if not provided
            userRating: item.userRating || '0',
            videoFinishTime: item.videoFinishTime,
            status: item.status,
            requestId: item.requestId,
          };
        });

        setState((prev) => ({
          ...prev,
          videoList: [...prev.videoList, ...newVideoList], // Append new videos
          nextUuid: result.nextUuid || null,
          hasNextPage: result.hasNext || false,
          isLoadingMore: false,
        }));

        // Start polling for new videos that need it
        checkAndStartPollingForVideos(newVideoList);
      } else {
        // No more videos
        setState((prev) => ({
          ...prev,
          hasNextPage: false,
          isLoadingMore: false,
        }));
      }
    } catch (error) {
      // Handle API error
      setState((prev) => ({
        ...prev,
        isLoadingMore: false,
      }));
      console.error('Load more videos failed:', error);
    }
  };

  // Handle step navigation
  const handleStepChange = (step: AIVideoState['currentStep']) => {
    setState((prev) => ({ ...prev, currentStep: step }));
  };

  // Handle form data update
  const handleFormUpdate = (updates: Partial<AIVideoState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  // Handle create new video action
  const handleCreateNewVideo = () => {
    // Clear form data when creating new video
    setState((prev) => ({
      ...prev,
      uploadedImage: null,
      positivePrompt: '',
      negativePrompt: '',
      quality: 'high',
      error: null,
      generatedVideoUrl: null,
      currentStep: isMobileDevice() ? 'form' : prev.currentStep,
    }));
  };

  // Handle refresh video list
  const handleRefreshVideoList = () => {
    loadVideoList();
    // Scroll to top when refreshing video list
    scrollToTop();
  };

  // Optimized single video update after regeneration
  const handleOptimizedVideoUpdate = async (regenerateResult: any, originalUuid: string) => {
    try {
      // If regenerate API returns a new UUID, it means a new video was created
      if (regenerateResult.uuid && regenerateResult.uuid !== originalUuid) {
        // Create new video info object for the regenerated video
        const currentTime = new Date().toISOString();
        const originalVideo = state.videoList.find((v) => v.uuid === originalUuid);

        if (originalVideo) {
          const newVideoInfo: VideoInfo = {
            uuid: regenerateResult.uuid,
            videoUrl: '', // Empty during processing
            imageUrl: originalVideo.imageUrl,
            positivePrompt: originalVideo.positivePrompt,
            negativePrompt: originalVideo.negativePrompt,
            quality: originalVideo.quality,
            duration: originalVideo.duration,
            status: 'processing',
            requestId: regenerateResult.uuid,
            createdAt: currentTime,
            loadVideoList,
          };

          // Add new video to the front of the list and start polling
          setState((prev) => ({
            ...prev,
            videoList: [newVideoInfo, ...prev.videoList],
          }));

          // Start polling for the new video
          startPollingForVideo(regenerateResult.uuid);
          return;
        }
      }

      // If no new UUID, check the status of the original video
      const statusResult = await checkVideoStatus({ uuid: originalUuid });

      if (statusResult) {
        // Update the specific video in the list
        setState((prev) => ({
          ...prev,
          videoList: prev.videoList.map((video) =>
            (video.uuid === originalUuid
              ? {
                ...video,
                status: statusResult.status || 'processing',
                videoUrl: statusResult.videoUrl || video.videoUrl,
                videoFinishTime: statusResult.finishTime || video.videoFinishTime,
              }
              : video)),
        }));

        // Start polling if the video is still processing
        if (statusResult.status === 'pending' || statusResult.status === 'processing') {
          startPollingForVideo(originalUuid);
        }
      } else {
        // If status check fails, fallback to full list reload
        loadVideoList();
      }
    } catch (error) {
      // Fallback to original behavior on any error
      loadVideoList();
    }
  };

  // Handle regenerate video from list
  const handleRegenerateFromList = (videoInfo: VideoInfo) => {
    // Update form with video info and start regeneration
    setState((prev) => ({
      ...prev,
      uploadedImage: videoInfo.imageUrl,
      positivePrompt: videoInfo.positivePrompt,
      negativePrompt: videoInfo.negativePrompt,
      quality: videoInfo.quality,
      currentStep: 'form',
      rightPanelContent: state.hasVideos ? 'videoList' : 'welcome',
    }));
  };

  // Handle video generation
  const handleGenerateVideo = async () => {
    if (!state.uploadedImage) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_PleaseUploadAnImage'),
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    if (!state.positivePrompt) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_PleaseEnterAForwardDescription'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    setState((prev) => ({
      ...prev,
      isGenerating: true,
      error: null,
      progress: 0,
      generatedVideoUrl: null,
    }));

    try {
      // Send generate event
      sendUT('ai_video_generate_click', {
        device: isMobileDevice() ? 'mobile' : 'pc',
        quality: state.quality,
        duration: 5,
        positivePrompt: state.positivePrompt,
        negativePrompt: state.negativePrompt,
        image: state.uploadedImage,
      });

      // Call video generation API
      const result = await generateVideo({
        imageInfo: JSON.stringify({
          url: state.uploadedImage,
        }),
        requirements: state.positivePrompt,
        restrictions: state.negativePrompt,
        quality: state.quality,
        duration: 5,
      });

      if (result.success && result.uuid) {
        // Create new video info object for processing state
        const currentTime = new Date().toISOString();
        const newVideoInfo: VideoInfo = {
          uuid: result.uuid,
          videoUrl: '', // Empty during processing
          imageUrl: state.uploadedImage || '',
          positivePrompt: state.positivePrompt,
          negativePrompt: state.negativePrompt,
          quality: state.quality,
          duration: 5,
          status: 'processing',
          requestId: result.uuid,
          createdAt: currentTime, // Set creation time for progress calculation
          loadVideoList,
        };

        // Calculate initial progress for the new video
        const initialProgress = calculateTimeBasedProgress(currentTime);

        setState((prev) => ({
          ...prev,
          taskId: result.uuid,
          // Add new processing video to the top of the list
          videoList: [newVideoInfo, ...prev.videoList],
          hasVideos: true,
          // Initialize progress for the new video
          progressMap: {
            ...prev.progressMap,
            [result.uuid]: initialProgress,
          },
          // Switch to video list view
          currentStep: isMobileDevice() ? 'videoList' : 'form',
          rightPanelContent: 'videoList',
        }));

        // Scroll to top when switching to video list
        scrollToTop();

        // Start polling for video generation status
        startPollingForVideo(result.uuid, true);
      } else {
        throw new Error(result?.errorMsg || i18next.t('j-dingtalk-web_pages_aiVideo_FailedToGenerate'));
      }
    } catch (error) {
      // console.error('Video generation failed:', error);
      setState((prev) => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : i18next.t('j-dingtalk-web_pages_aiVideo_FailedToGeneratePleaseTry'),
        progress: 0,
      }));
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_VideoGenerationFailedPleaseTry'),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    }
  };

  // Render PC layout with left-right structure
  const renderPCLayout = () => {
    // Show loading overlay if videos are loading
    if (state.isLoadingVideos) {
      return (
        <div className="ai-video-pc-layout">
          <Loading text={i18next.t('j-dingtalk-web_pages_aiVideo_Loading')} />
        </div>);
    }

    return (
      <div className="ai-video-pc-layout">
        {/* Left Panel - Always show VideoForm */}
        <div className="ai-video-left-panel">
          <VideoForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateVideo}
          />

        </div>

        {/* Right Panel - Show Welcome, VideoList, or Preview based on state */}
        <div className="ai-video-right-panel">
          {state.rightPanelContent === 'welcome' &&
          <WelcomeScreen
            hasGenerateBtn={false}
            onGetStarted={() => {
              // On PC, clicking "Get Started" doesn't change layout, just focuses on form
              // Could add some visual feedback here if needed
            }}
          />}

          {
            state.rightPanelContent === 'videoList' &&
              <VideoList
                videoList={state.videoList}
                isLoading={false} // Loading is handled at page level
                error={state.videoListError}
                onCreateNew={handleCreateNewVideo}
                onRegenerate={handleRegenerateFromList}
                onRefresh={handleRefreshVideoList}
                progressMap={state.progressMap}
                hasNextPage={state.hasNextPage}
                isLoadingMore={state.isLoadingMore}
                onLoadMore={loadMoreVideos}
                loadVideoList={loadVideoList}
                onOptimizedVideoUpdate={handleOptimizedVideoUpdate}
              />
          }
        </div>
      </div>);
  };

  // Render mobile layout (updated with video list support)
  const renderMobileLayout = () => {
    // Show loading screen if videos are loading
    if (state.isLoadingVideos) {
      return <Loading text={i18next.t('j-dingtalk-web_pages_aiVideo_Loading')} />;
    }

    switch (state.currentStep) {
      case 'welcome':
        return (
          <WelcomeScreen
            hasGenerateBtn
            onGetStarted={() => handleStepChange('form')}
          />);
      case 'form':
        return (
          <VideoForm
            state={state}
            onUpdate={handleFormUpdate}
            onGenerate={handleGenerateVideo}
          />);
      case 'videoList':
        return (
          <VideoList
            videoList={state.videoList}
            isLoading={false} // Loading is handled at page level
            error={state.videoListError}
            onCreateNew={handleCreateNewVideo}
            onRegenerate={handleRegenerateFromList}
            onRefresh={handleRefreshVideoList}
            progressMap={state.progressMap}
            hasNextPage={state.hasNextPage}
            isLoadingMore={state.isLoadingMore}
            onLoadMore={loadMoreVideos}
            loadVideoList={loadVideoList}
            onOptimizedVideoUpdate={handleOptimizedVideoUpdate}
          />);
      default:
        return null;
    }
  };

  return (
    <div className="ai-video-page">
      {isMobileDevice() ? renderMobileLayout() : renderPCLayout()}
    </div>);
};

export default AIVideoPage;
