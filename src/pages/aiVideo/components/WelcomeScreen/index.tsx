import React from 'react';
import { Button } from 'dingtalk-design-mobile';
import { VideoPlayOutlined } from '@ali/ding-icons';
import './index.less';

interface WelcomeScreenProps {
  hasGenerateBtn: boolean;
  onGetStarted: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ hasGenerateBtn, onGetStarted }) => {
  return (
    <div className="welcome-screen">
      <div className="welcome-content">
        <div className="logo-section">
          <VideoPlayOutlined className="logo-image" />
          <h1 className="title">AI视频生成</h1>
          <p className="subtitle">
            制作高质量的电商产品视频<br />
            从未如此简单
          </p>
        </div>

        {hasGenerateBtn && (
          <Button
            type="primary"
            size="large"
            className="get-started-btn"
            onClick={onGetStarted}
          >
            Get start
          </Button>
        )}
      </div>
    </div>
  );
};

export default WelcomeScreen;
