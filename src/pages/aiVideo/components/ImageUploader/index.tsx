import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect, useRef } from 'react';
import { Button, Toast } from 'dingtalk-design-mobile';
import $uploadImage from '@ali/dingtalk-jsapi/api/biz/util/uploadImage';
import { AddToSFilled, ResumePreviewOutlined, DeleteOutlined } from '@ali/ding-icons';
import { previewImages } from '@/components/imagesPreview';
import { isDingTalk, configDingTalkImageUpload } from '@/utils/jsapi';
import './index.less';

interface ImageSizeConstraints {
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

interface ImageUploaderProps {
  value?: string | null;
  onChange?: (imageUrl: string | null) => void;
  sizeConstraints?: ImageSizeConstraints; // 图片尺寸限制配置
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  value,
  onChange,
  sizeConstraints, // 不设置默认值，如果没有传递则不校验
}) => {
  const [loading, setLoading] = useState(false);
  const [apiConfigured, setApiConfigured] = useState(false);
  const uploadAreaRef = useRef<HTMLDivElement>(null);

  // Configure DingTalk JS-API on component mount
  useEffect(() => {
    if (isDingTalk()) {
      configDingTalkImageUpload()
        .then(() => {
          setApiConfigured(true);
        })
        .catch((error) => {
        // eslint-disable-next-line no-console
          console.error('Failed to configure DingTalk JS-API:', error);
          setApiConfigured(false);
        });
    } else {
      // Not in DingTalk environment, disable API features
      setApiConfigured(false);
    }
  }, []);

  // Auto focus on upload area when component mounts and no image is uploaded
  useEffect(() => {
    if (!value && uploadAreaRef.current) {
      // Set focus on the upload area for accessibility
      uploadAreaRef.current.focus();
    }
  }, [value]);

  // 验证图片尺寸的函数 - Validate image dimensions
  const validateImageSize = (imageUrl: string): Promise<boolean> => {
    return new Promise((resolve) => {
      // 如果没有传递尺寸限制参数，则跳过验证 - Skip validation if no size constraints provided
      if (!sizeConstraints) {
        resolve(true);
        return;
      }

      const img = new Image();
      img.onload = () => {
        const { width, height } = img;
        const {
          minWidth = 0,
          minHeight = 0,
          maxWidth = Infinity,
          maxHeight = Infinity,
        } = sizeConstraints;

        // 检查尺寸是否符合要求 - Check if dimensions meet requirements
        const isValid = width >= minWidth &&
                       height >= minHeight &&
                       width <= maxWidth &&
                       height <= maxHeight;

        if (!isValid) {
          // 显示尺寸错误提示 - Show dimension error message
          Toast.fail({
            content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageSizeError', {
              minWidth,
              minHeight,
              maxWidth,
              maxHeight,
            }),
            position: 'top',
            maskClickable: true,
            duration: 3,
          });
        }

        resolve(isValid);
      };
      img.onerror = () => {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageLoadError'),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
        resolve(false);
      };
      img.src = imageUrl;
    });
  };

  const handlePreview = () => {
    if (value) {
      previewImages({
        photos: [{ src: value }],
        current: 0,
      });
    }
  };

  const handleRemove = () => {
    onChange?.(null);
  };

  const handleUploadClick = () => {
    // Check if in DingTalk environment and API is configured
    if (!isDingTalk()) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_UseThisFeatureInThe'),
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    if (!apiConfigured) {
      Toast.info({
        content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_InitializingPleaseTryAgainLater'),
        position: 'top',
        maskClickable: true,
        duration: 2,
      });
      return;
    }

    setLoading(true);

    // Use DingTalk biz.util.uploadImage API with .then callback
    $uploadImage({
      multiple: false, // Single image upload
      compression: true, // Disable image compression
      max: 1, // Maximum 1 image
    }).then(async (result: any) => {
      // Handle upload success
      if (result && result.length > 0) {
        // Get the first uploaded image URL
        const imageUrl = result[0];

        // 验证图片尺寸 - Validate image dimensions
        const isValidSize = await validateImageSize(imageUrl);

        if (isValidSize) {
          onChange?.(imageUrl);
          Toast.success({
            content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully'),
            position: 'top',
            maskClickable: true,
            duration: 2,
          });
        }
        // 如果尺寸不符合要求，validateImageSize 函数内部已经显示了错误提示
        // If dimensions don't meet requirements, error message is already shown
      } else {
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed'),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
      }
      setLoading(false);
    }).catch((error: any) => {
      // Handle upload failure
      // eslint-disable-next-line no-console
      console.error('Image upload error:', error);
      setLoading(false);
    });
  };

  return (
    <div className="image-uploader">
      {value ?
        <div className="uploaded-image">
          <img src={value} alt="Uploaded" className="preview-img" />
          <div className="image-actions">
            <Button
              size="small"
              onClick={handlePreview}
              className="action-btn preview-btn"
            >

              <ResumePreviewOutlined />
            </Button>
            <Button
              size="small"
              onClick={handleRemove}
              className="action-btn remove-btn danger"
            >

              <DeleteOutlined />
            </Button>
          </div>
        </div> :

        <div
          ref={uploadAreaRef}
          className="upload-area"
          onClick={handleUploadClick}
          tabIndex={0}
          role="button"
          aria-label={i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage')}
          onKeyDown={(e) => {
            // Handle Enter and Space key for accessibility
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleUploadClick();
            }
          }}
        >
          <AddToSFilled className="upload-icon" />
          <div className="upload-text">{i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage')}

          </div>
        </div>
      }

      {loading &&
      <div className="upload-loading">
        <div className="loading-spinner" />
        <span>{i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading')}</span>
      </div>
      }
    </div>);
};

export default ImageUploader;
