.ai-studio-page {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  background:
    radial-gradient(ellipse 600px 500px at top right,
      transparent 0%,
      transparent 30%,
      rgba(20, 20, 20, 0.1) 35%,
      rgba(20, 20, 20, 0.3) 45%,
      rgba(20, 20, 20, 0.5) 55%,
      rgba(20, 20, 20, 0.7) 65%,
      rgba(20, 20, 20, 0.85) 75%,
      #141414 85%
    ),
    linear-gradient(180deg, #6B1538 10%, #2F1624 100%);
  backdrop-filter: blur(40px);

  // 添加模糊过渡效果的伪元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 70%;
    height: 60%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.15) 0%,
      rgba(107, 21, 56, 0.1) 20%,
      rgba(47, 22, 36, 0.08) 40%,
      rgba(47, 22, 36, 0.05) 60%,
      transparent 80%
    );
    filter: blur(50px);
    pointer-events: none;
    z-index: 1;
  }

  // 添加第二层更柔和的模糊效果
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 80%;
    height: 70%;
    background: radial-gradient(ellipse at top right,
      rgba(107, 21, 56, 0.08) 0%,
      rgba(47, 22, 36, 0.05) 30%,
      rgba(20, 20, 20, 0.03) 50%,
      transparent 70%
    );
    filter: blur(80px);
    pointer-events: none;
    z-index: 0;
  }

  // Mobile 端导航栏
  .mobile-navbar {
    display: none; // 默认隐藏，在移动端显示
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 44px;
    background: transparent;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    padding-top: env(safe-area-inset-top);
    height: calc(44px + env(safe-area-inset-top));

    .nav-button {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.9);
      font-size: 22px;
      font-weight: bold;
      padding: 8px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 44px;
      height: 44px;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.7;
      }

      &:active {
        opacity: 0.5;
      }
    }
  }

  .container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px 16px;
    padding-top: calc(24px + 44px + env(safe-area-inset-top));
    margin: 0 auto;
    max-width: 1200px; // PC 端最大宽度限制
    z-index: 2;
  }

  // 主要内容区域
  .main-content {
    text-align: center;
    margin-bottom: 40px;
    // AI 图标容器
    .main-logo {
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
    }

    .page-title {
      font-size: 24px;
      font-weight: 500;
      line-height: 34px;
      color: rgba(255, 255, 255, 0.9);
      text-align: center;
    }
  }

  // 功能卡片容器
  .feature-cards {
    display: flex;
    flex-direction: column; // Mobile 端默认纵向布局
    gap: 12px;
    width: 100%;

    .feature-card {
      background: #222224;
      border-radius: 24px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: darken(#222224, 1%);
        transform: translateY(-2px);
      }

      &:active {
        background: darken(#222224, 2%);
        transform: translateY(0);
      }

      .card-content {
        display: flex;
        align-items: flex-start;
        flex-direction: column;

        .card-icon {
          width: 40px;
          height: 40px;
          font-size: 40px;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 8px;
        }
        .card-title {
          font-size: 18px;
          font-weight: 500;
          line-height: 24px;
          color: rgba(255, 255, 255, 0.9);
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          gap: 8px;

          .arrow-icon {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .card-description {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.5);
          line-height: 1.4;
        }
      }
    }
  }

  // PC 端样式 (屏幕宽度大于 768px)
  @media (min-width: 768px) {
    // 隐藏移动端导航栏
    .mobile-navbar {
      display: none !important;
    }

    .container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100vh;
      padding-top: 24px; // PC 端减少顶部间距
      max-width: 1200px;
      margin: 0 auto;
    }

    .main-content {
      margin-bottom: 60px; // PC 端增加底部间距
    }

    // PC 端功能卡片横向布局
    .feature-cards {
      flex-direction: row; // PC 端横向布局
      gap: 24px;
      justify-content: center;
      max-width: 800px;

      .feature-card {
        flex: 1;
        max-width: 380px;
      }
    }
  }

  // Mobile 端样式 (屏幕宽度小于等于 768px)
  @media (max-width: 768px) {
    // 显示移动端导航栏
    .mobile-navbar {
      display: flex !important;
    }

    .container {
      padding-top: calc(24px + 44px + env(safe-area-inset-top)); // 为导航栏留出空间
    }

    // Mobile 端功能卡片纵向布局
    .feature-cards {
      flex-direction: column; // Mobile 端纵向布局
      gap: 12px;
      width: 100%;

      .feature-card {
        width: 100%;
      }
    }
  }

  // 针对钉钉环境的特殊处理
  @media (max-width: 768px) {
    // 钉钉移动端环境下的样式调整
    body.dingtalk-mobile & {
      .mobile-navbar {
        background: transparent;
      }
    }
  }
}
