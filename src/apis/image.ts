import request from './base';

// 抠图请求参数接口 (Extract image request parameters interface)
export interface ExtractImageRequest {
  imgUrl: string; // 原图片URL (Original image URL)
}

// 抠图响应结果接口 (Extract image response interface)
export interface ExtractImageResponse {
  imgUrl: string; // 处理后的图片URL，成功时为抠图结果，失败时应为原图URL (Processed image URL)
  success: boolean; // 抠图是否成功 (Whether extraction was successful)
  errorMsg: string; // 错误信息，成功时可为空 (Error message, can be empty on success)
}

// 抠图服务 (Image extraction service)
export const extractImage = (data: ExtractImageRequest): Promise<ExtractImageResponse> => {
  return request('/r/Adaptor/MaterialRpcI/extractImage', [data]);
};

// 生成图片
export const generateImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/generateImage', [data]);
};

// 查看图片生成状态
export const checkImageStatus = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/checkImageStatus', [data]);
};

// 生成高清大图
export const generateHDImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/generateHDImage', [data]);
};

// 获取图片列表
export const listImages = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/listImages', [data]);
};

// 删除图片
export const deleteImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/deleteImage', [data]);
};

// 重新生成图片
export const reGenerateImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/reGenerateImage', [data]);
};

// 评价图片
export const rateImage = (data: {} = {}) => {
  return request('/r/Adaptor/MaterialRpcI/rateImage', [data]);
};
